#!/usr/bin/env python3
"""
115网盘磁力链接添加脚本
通过浏览器自动化添加磁力下载任务
"""

import time
import sys
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.keys import Keys

def add_magnet_to_115(magnet_url):
    """添加磁力链接到115网盘"""
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--remote-debugging-port=9222')
    
    # 使用现有的115Browser进程
    chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
    
    try:
        # 连接到现有浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 导航到115网盘离线下载页面
        print("正在打开115网盘离线下载页面...")
        driver.get("https://115.com/?cid=0&offset=0&tab=offline")
        
        # 等待页面加载
        time.sleep(3)
        
        # 查找添加任务按钮
        try:
            # 尝试多种可能的选择器
            selectors = [
                "//button[contains(text(), '添加任务')]",
                "//a[contains(text(), '添加任务')]", 
                "//span[contains(text(), '添加任务')]",
                "//div[contains(@class, 'add-task')]",
                "//button[contains(@class, 'add')]",
                "//*[@id='js_offline_new_add']"
            ]
            
            add_button = None
            for selector in selectors:
                try:
                    add_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    print(f"找到添加按钮: {selector}")
                    break
                except:
                    continue
            
            if not add_button:
                print("未找到添加任务按钮，尝试手动查找...")
                # 打印页面源码的一部分来调试
                page_source = driver.page_source
                if "添加任务" in page_source:
                    print("页面包含'添加任务'文本")
                else:
                    print("页面不包含'添加任务'文本，可能需要登录")
                    return False
            
            # 点击添加任务按钮
            add_button.click()
            print("已点击添加任务按钮")
            time.sleep(2)
            
            # 查找磁力链接输入框
            url_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='text' or @type='url']"))
            )
            
            # 输入磁力链接
            url_input.clear()
            url_input.send_keys(magnet_url)
            print(f"已输入磁力链接: {magnet_url}")
            
            # 查找确认按钮
            confirm_selectors = [
                "//button[contains(text(), '确定')]",
                "//button[contains(text(), '添加')]",
                "//button[contains(text(), '开始')]",
                "//input[@type='submit']"
            ]
            
            confirm_button = None
            for selector in confirm_selectors:
                try:
                    confirm_button = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    break
                except:
                    continue
            
            if confirm_button:
                confirm_button.click()
                print("已点击确认按钮")
                time.sleep(3)
                print("磁力链接添加成功！")
                return True
            else:
                print("未找到确认按钮")
                return False
                
        except Exception as e:
            print(f"操作过程中出错: {e}")
            return False
            
    except Exception as e:
        print(f"连接浏览器失败: {e}")
        print("请确保115Browser正在运行并开启了远程调试")
        return False
    
    finally:
        try:
            driver.quit()
        except:
            pass

def main():
    if len(sys.argv) != 2:
        print("用法: python3 add_115_magnet.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    
    if not magnet_url.startswith("magnet:"):
        print("错误: 请提供有效的磁力链接")
        sys.exit(1)
    
    print(f"准备添加磁力链接: {magnet_url}")
    
    success = add_magnet_to_115(magnet_url)
    
    if success:
        print("磁力链接添加成功！")
        sys.exit(0)
    else:
        print("磁力链接添加失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()