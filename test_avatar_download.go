package main

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

func downloadActorAvatar(avatarURL, actorName string) error {
	actorDir := "/www/wwwroot/JAVAPI.COM/storage/actors"
	os.MkdirAll(actorDir, 0755)
	
	safeName := strings.ReplaceAll(actorName, " ", "_")
	filename := fmt.Sprintf("%s.jpg", safeName)
	localPath := filepath.Join(actorDir, filename)
	
	client := &http.Client{Timeout: 30 * time.Second}
	req, _ := http.NewRequest("GET", avatarURL, nil)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
	req.Header.Set("Referer", "https://www.javbus.com/")
	req.Header.Set("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
	
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != 200 {
		return fmt.Errorf("HTTP %d", resp.StatusCode)
	}
	
	file, err := os.Create(localPath)
	if err != nil {
		return err
	}
	defer file.Close()
	
	_, err = io.Copy(file, resp.Body)
	return err
}

func main() {
	// 测试一个示例头像URL
	testURL := "https://www.javbus.com/pics/actress/okq_a.jpg"
	testName := "测试演员"
	
	fmt.Printf("🧪 测试演员头像下载功能...\n")
	fmt.Printf("📸 测试URL: %s\n", testURL)
	fmt.Printf("👤 演员名: %s\n", testName)
	
	err := downloadActorAvatar(testURL, testName)
	if err != nil {
		fmt.Printf("❌ 下载失败: %v\n", err)
	} else {
		fmt.Printf("✅ 下载成功！\n")
		
		// 检查文件
		files, _ := os.ReadDir("/www/wwwroot/JAVAPI.COM/storage/actors")
		fmt.Printf("📁 目录内容:\n")
		for _, file := range files {
			info, _ := file.Info()
			fmt.Printf("  📄 %s (%.2f KB)\n", file.Name(), float64(info.Size())/1024)
		}
	}
}