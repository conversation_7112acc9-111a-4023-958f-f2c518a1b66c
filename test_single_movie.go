package main

import (
	"fmt"
	"log"
	"os"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/repository"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/javscraper"
	"magnet-downloader/pkg/logger"
)

func main() {
	// 初始化日志
	logger.Init()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	repo, err := repository.New(cfg.Database.DSN)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	fmt.Println("✅ 数据库连接成功")

	// 初始化服务
	javService := service.NewJAVService(repo)
	aria2Service := service.NewAria2Service(cfg.Aria2.URL, cfg.Aria2.Secret)

	// 初始化爬虫管理器
	scraperManager := javscraper.NewManager()

	// 测试单个影片
	movieCode := "HODV-21978"
	fmt.Printf("🎬 测试采集影片: %s\n", movieCode)

	// 1. 数据采集
	result, err := scraperManager.ScrapeMovie(movieCode)
	if err != nil {
		log.Fatalf("采集失败: %v", err)
	}

	fmt.Printf("✅ 数据采集成功 (来源: %s)\n", result.Source)
	fmt.Printf("📝 影片信息: %s - %s\n", result.Movie.Code, result.Movie.Title)
	fmt.Printf("🎬 工作室: %s\n", result.Movie.Studio)
	fmt.Printf("👥 演员数量: %d\n", len(result.Movie.Actors))

	// 打印演员信息
	for i, actor := range result.Movie.Actors {
		fmt.Printf("  演员%d: %s\n", i+1, actor.Name)
		fmt.Printf("    头像URL: %s\n", actor.AvatarURL)
		fmt.Printf("    英文名: %s\n", actor.NameEn)
		fmt.Printf("    日文名: %s\n", actor.NameJp)
	}

	// 2. 测试演员头像下载
	if len(result.Movie.Actors) > 0 {
		fmt.Printf("\n🖼️  开始测试演员头像下载...\n")
		
		// 创建演员目录
		actorDir := "/www/wwwroot/JAVAPI.COM/storage/actors"
		err := os.MkdirAll(actorDir, 0755)
		if err != nil {
			fmt.Printf("❌ 创建演员目录失败: %v\n", err)
		} else {
			fmt.Printf("✅ 演员目录创建成功: %s\n", actorDir)
		}

		// 下载每个演员的头像
		for i, actor := range result.Movie.Actors {
			fmt.Printf("\n👤 处理演员%d: %s\n", i+1, actor.Name)
			
			if actor.AvatarURL == "" {
				fmt.Printf("  ⚠️  演员头像URL为空，跳过下载\n")
				continue
			}

			fmt.Printf("  🔗 头像URL: %s\n", actor.AvatarURL)
			
			// 调用下载函数
			err := downloadActorAvatar(actor.AvatarURL, actor.Name)
			if err != nil {
				fmt.Printf("  ❌ 头像下载失败: %v\n", err)
			} else {
				fmt.Printf("  ✅ 头像下载成功\n")
			}
		}
	}

	fmt.Printf("\n🎉 测试完成！\n")
}