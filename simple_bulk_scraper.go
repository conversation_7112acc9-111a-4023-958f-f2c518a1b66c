package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"
)

// JavBusAPI响应结构
type JavBusMoviesResponse struct {
	Movies     []JavBusMovie `json:"movies"`
	Pagination Pagination    `json:"pagination"`
}

type JavBusMovie struct {
	ID       string `json:"id"`
	Title    string `json:"title"`
	Code     string `json:"code"`
	Date     string `json:"date"`
	CoverURL string `json:"coverUrl"`
	URL      string `json:"url"`
}

type Pagination struct {
	CurrentPage int   `json:"currentPage"`
	HasNextPage bool  `json:"hasNextPage"`
	NextPage    *int  `json:"nextPage"`
	Pages       []int `json:"pages"`
}

// 采集统计
type ScrapeStats struct {
	TotalPages      int
	TotalMovies     int
	ProcessedMovies int
	StartTime       time.Time
	EndTime         time.Time
	Errors          []string
}

func main() {
	fmt.Println("🚀 JavBus简化批量分页采集器")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 初始化统计
	stats := &ScrapeStats{
		StartTime: time.Now(),
		Errors:    []string{},
	}

	fmt.Println("📋 开始JavBus批量分页采集...")
	fmt.Printf("⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Println()

	// 执行批量采集
	err := performBulkScraping(stats)
	if err != nil {
		log.Fatalf("❌ 批量采集失败: %v", err)
	}

	// 显示最终统计
	showFinalStats(stats)
}

func performBulkScraping(stats *ScrapeStats) error {
	javbusAPIURL := "http://localhost:3001/api/movies"
	currentPage := 1
	maxPages := 1000 // 设置足够大的数值，实际会在没有下一页时自动停止

	fmt.Printf("🔍 开始从第1页采集，最大页数限制: %d\n", maxPages)
	fmt.Println()

	for currentPage <= maxPages {
		fmt.Printf("📄 正在采集第 %d 页...\n", currentPage)

		// 获取当前页的影片列表
		movies, pagination, err := fetchMoviesFromPage(javbusAPIURL, currentPage)
		if err != nil {
			errorMsg := fmt.Sprintf("获取第%d页失败: %v", currentPage, err)
			stats.Errors = append(stats.Errors, errorMsg)
			fmt.Printf("  ❌ %s\n", errorMsg)
			
			// 如果是404错误，说明已经到达最后一页
			if strings.Contains(err.Error(), "404") {
				fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage-1)
				break
			}
			
			// 其他错误，跳过当前页继续
			currentPage++
			continue
		}

		stats.TotalPages = currentPage
		stats.TotalMovies += len(movies)

		fmt.Printf("  📊 找到 %d 部影片\n", len(movies))

		// 显示影片信息
		for i, movie := range movies {
			if i < 5 { // 只显示前5部
				fmt.Printf("    %d. %s - %s\n", i+1, movie.Code, movie.Title)
			}
		}
		if len(movies) > 5 {
			fmt.Printf("    ... 还有 %d 部影片\n", len(movies)-5)
		}

		stats.ProcessedMovies += len(movies)

		// 检查是否有下一页
		if !pagination.HasNextPage {
			fmt.Printf("✅ 已到达最后一页 (第%d页)\n", currentPage)
			break
		}

		// 页面间休息，避免对服务器造成压力
		fmt.Printf("  ⏳ 休息2秒后继续下一页...\n")
		time.Sleep(2 * time.Second)
		fmt.Println()

		currentPage++
	}

	stats.EndTime = time.Now()
	return nil
}

func fetchMoviesFromPage(baseURL string, page int) ([]JavBusMovie, *Pagination, error) {
	url := fmt.Sprintf("%s?page=%d", baseURL, page)
	
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Get(url)
	if err != nil {
		return nil, nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == 404 {
		return nil, nil, fmt.Errorf("页面不存在 (404)")
	}

	if resp.StatusCode != 200 {
		return nil, nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
	}

	var response JavBusMoviesResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return nil, nil, fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return response.Movies, &response.Pagination, nil
}

func showFinalStats(stats *ScrapeStats) {
	stats.EndTime = time.Now()
	duration := stats.EndTime.Sub(stats.StartTime)

	fmt.Println()
	fmt.Println("🎉 JavBus批量分页采集完成！")
	fmt.Println("=" + strings.Repeat("=", 50))
	fmt.Printf("📊 采集统计:\n")
	fmt.Printf("  总页数: %d\n", stats.TotalPages)
	fmt.Printf("  发现影片: %d 部\n", stats.TotalMovies)
	fmt.Printf("  处理影片: %d 部\n", stats.ProcessedMovies)
	fmt.Printf("  总耗时: %v\n", duration)
	if stats.TotalPages > 0 {
		fmt.Printf("  平均每页: %v\n", duration/time.Duration(stats.TotalPages))
	}
	if stats.ProcessedMovies > 0 {
		fmt.Printf("  平均每部: %v\n", duration/time.Duration(stats.ProcessedMovies))
	}

	if len(stats.Errors) > 0 {
		fmt.Printf("\n⚠️  错误信息 (%d个):\n", len(stats.Errors))
		for i, err := range stats.Errors {
			if i < 5 { // 只显示前5个错误
				fmt.Printf("  %d. %s\n", i+1, err)
			}
		}
		if len(stats.Errors) > 5 {
			fmt.Printf("  ... 还有 %d 个错误\n", len(stats.Errors)-5)
		}
	}

	fmt.Printf("\n⏰ 开始时间: %s\n", stats.StartTime.Format("2006-01-02 15:04:05"))
	fmt.Printf("⏰ 结束时间: %s\n", stats.EndTime.Format("2006-01-02 15:04:05"))
	fmt.Println()
	fmt.Println("🎯 分页采集测试完成！")
}