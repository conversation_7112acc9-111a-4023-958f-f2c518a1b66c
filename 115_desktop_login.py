#!/usr/bin/env python3
"""
115网盘桌面客户端登录助手
"""

import subprocess
import time
import os
import signal
import sys

def start_115_browser():
    """启动115浏览器客户端"""
    print("🚀 启动115网盘客户端...")
    
    try:
        # 启动115客户端
        process = subprocess.Popen(
            ['/usr/local/bin/115browser'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
        
        print(f"✅ 115客户端已启动 (PID: {process.pid})")
        print("📱 请在115客户端中登录您的账号")
        print("💡 登录步骤:")
        print("   1. 客户端会自动打开登录页面")
        print("   2. 输入您的115账号和密码")
        print("   3. 或使用手机APP扫码登录")
        print("   4. 登录成功后即可使用115网盘功能")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动115客户端失败: {e}")
        return None

def check_115_process():
    """检查115进程是否运行"""
    try:
        result = subprocess.run(
            ['pgrep', '-f', '115Browser'],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            return [pid for pid in pids if pid]
        else:
            return []
            
    except Exception as e:
        print(f"检查进程时出错: {e}")
        return []

def stop_115_browser():
    """停止115客户端"""
    pids = check_115_process()
    
    if not pids:
        print("❌ 没有找到运行中的115客户端")
        return False
    
    print(f"🛑 停止115客户端进程: {', '.join(pids)}")
    
    try:
        for pid in pids:
            os.kill(int(pid), signal.SIGTERM)
        
        time.sleep(2)
        
        # 检查是否还有进程运行
        remaining_pids = check_115_process()
        if remaining_pids:
            print("强制终止剩余进程...")
            for pid in remaining_pids:
                os.kill(int(pid), signal.SIGKILL)
        
        print("✅ 115客户端已停止")
        return True
        
    except Exception as e:
        print(f"❌ 停止115客户端失败: {e}")
        return False

def show_status():
    """显示115客户端状态"""
    pids = check_115_process()
    
    if pids:
        print("✅ 115客户端正在运行")
        print(f"📊 进程ID: {', '.join(pids)}")
        
        # 显示进程详细信息
        try:
            for pid in pids:
                result = subprocess.run(
                    ['ps', '-p', pid, '-o', 'pid,ppid,cmd'],
                    capture_output=True,
                    text=True
                )
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    if len(lines) > 1:
                        print(f"   {lines[1]}")
        except:
            pass
    else:
        print("❌ 115客户端未运行")

def main():
    if len(sys.argv) < 2:
        print("115网盘桌面客户端管理工具")
        print("用法:")
        print("  python3 115_desktop_login.py start   # 启动115客户端")
        print("  python3 115_desktop_login.py stop    # 停止115客户端")
        print("  python3 115_desktop_login.py status  # 查看运行状态")
        print("  python3 115_desktop_login.py restart # 重启115客户端")
        return
    
    command = sys.argv[1]
    
    if command == 'start':
        # 检查是否已经运行
        if check_115_process():
            print("⚠️  115客户端已经在运行中")
            show_status()
        else:
            process = start_115_browser()
            if process:
                print("\n💡 提示:")
                print("   - 使用 'python3 115_desktop_login.py status' 查看状态")
                print("   - 使用 'python3 115_desktop_login.py stop' 停止客户端")
                print("   - 客户端在后台运行，可以关闭此终端")
    
    elif command == 'stop':
        stop_115_browser()
    
    elif command == 'status':
        show_status()
    
    elif command == 'restart':
        print("🔄 重启115客户端...")
        stop_115_browser()
        time.sleep(2)
        start_115_browser()
    
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()