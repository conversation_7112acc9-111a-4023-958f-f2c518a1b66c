#!/bin/bash

# JAV采集器多模式启动脚本

echo "🎬 JAV采集器启动选择"
echo "===================="
echo "1. 测试模式 (5页)"
echo "2. 中等模式 (50页)" 
echo "3. 大量模式 (500页)"
echo "4. 无限模式 (直到结束)"
echo "5. 自定义页数"
echo ""

read -p "请选择模式 (1-5): " choice

case $choice in
    1)
        echo "🧪 启动测试模式 (5页)..."
        sed -i 's/maxPages := [0-9]*/maxPages := 5/' /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
    2)
        echo "📚 启动中等模式 (50页)..."
        sed -i 's/maxPages := [0-9]*/maxPages := 50/' /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
    3)
        echo "📖 启动大量模式 (500页)..."
        sed -i 's/maxPages := [0-9]*/maxPages := 500/' /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
    4)
        echo "🚀 启动无限模式 (直到结束)..."
        sed -i 's/maxPages := [0-9]*/maxPages := 99999/' /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
    5)
        read -p "请输入要采集的页数: " custom_pages
        echo "🎯 启动自定义模式 ($custom_pages页)..."
        sed -i "s/maxPages := [0-9]*/maxPages := $custom_pages/" /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
    *)
        echo "❌ 无效选择，使用默认测试模式 (5页)"
        sed -i 's/maxPages := [0-9]*/maxPages := 5/' /www/wwwroot/JAVAPI.COM/cmd/complete_javbus_scraper/main.go
        ;;
esac

echo ""
echo "🚀 启动采集器..."
cd /www/wwwroot/JAVAPI.COM

# 启动采集器
make scraper