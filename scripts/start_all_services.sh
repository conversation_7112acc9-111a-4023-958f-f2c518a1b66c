#!/bin/bash

# 启动所有JAV数据采集服务的脚本

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "🚀 启动JAV数据采集系统所有服务..."
echo "项目根目录: $PROJECT_ROOT"
echo "=" * 60

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "⚠️  端口 $port 已被占用 ($service)"
        return 1
    else
        echo "✅ 端口 $port 可用 ($service)"
        return 0
    fi
}

# 启动服务并等待
start_service() {
    local service_name=$1
    local port=$2
    local start_command=$3
    local health_endpoint=$4
    
    echo ""
    echo "🔧 启动 $service_name (端口 $port)..."
    
    # 检查端口
    if ! check_port $port "$service_name"; then
        echo "跳过 $service_name - 端口已被占用"
        return 0
    fi
    
    # 启动服务
    echo "执行命令: $start_command"
    eval "$start_command" &
    local pid=$!
    
    # 等待服务启动
    echo "等待 $service_name 启动..."
    for i in {1..30}; do
        sleep 1
        if curl -s "$health_endpoint" >/dev/null 2>&1; then
            echo "✅ $service_name 启动成功 (PID: $pid)"
            return 0
        fi
        echo -n "."
    done
    
    echo ""
    echo "❌ $service_name 启动失败或超时"
    return 1
}

# 停止现有服务
stop_existing_services() {
    echo "🛑 停止现有服务..."
    
    # 停止JavSP包装器
    pkill -f "python3 simple_wrapper.py" 2>/dev/null || true
    
    # 停止Javinizer包装器
    pkill -f "node wrapper-server.js" 2>/dev/null || true
    
    # 等待进程完全停止
    sleep 2
    echo "✅ 现有服务已停止"
}

# 主函数
main() {
    cd "$PROJECT_ROOT"
    
    # 停止现有服务
    stop_existing_services
    
    # 启动JavBus API (假设已经在运行)
    echo ""
    echo "🔍 检查JavBus API (端口 3001)..."
    if curl -s "http://localhost:3001/health" >/dev/null 2>&1; then
        echo "✅ JavBus API 已在运行"
    else
        echo "⚠️  JavBus API 未运行，请手动启动"
    fi
    
    # 启动JavSP包装器
    start_service "JavSP包装器" 3002 \
        "cd external_scrapers/JavSP && nohup python3 simple_wrapper.py > wrapper.log 2>&1" \
        "http://localhost:3002/health"
    
    # 启动Javinizer包装器
    start_service "Javinizer包装器" 3003 \
        "cd external_scrapers/Javinizer && nohup node wrapper-server.js > wrapper.log 2>&1" \
        "http://localhost:3003/health"
    
    echo ""
    echo "=" * 60
    echo "🎉 所有服务启动完成！"
    echo ""
    echo "服务状态:"
    echo "  JavBus API:      http://localhost:3001"
    echo "  JavSP包装器:     http://localhost:3002"
    echo "  Javinizer包装器: http://localhost:3003"
    echo ""
    echo "健康检查:"
    echo "  curl http://localhost:3001/health"
    echo "  curl http://localhost:3002/health"
    echo "  curl http://localhost:3003/health"
    echo ""
    echo "运行集成测试:"
    echo "  go run test_integration.go"
}

# 信号处理
trap 'echo ""; echo "🛑 收到中断信号，正在停止服务..."; stop_existing_services; exit 0' INT TERM

# 执行主函数
main "$@"