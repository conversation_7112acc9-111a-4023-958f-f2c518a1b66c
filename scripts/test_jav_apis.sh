#!/bin/bash

# JAV API端点测试脚本

set -e

echo "🧪 JAV API端点测试"
echo "=" * 50

# 测试配置
BASE_URL="http://localhost:8080"
TEST_CODE="LULU-383"
TEST_ACTRESS="佐山由依"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_api() {
    local endpoint=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -n "测试 $description ... "
    
    response=$(curl -s -w "%{http_code}" -o /tmp/api_response.json "$BASE_URL$endpoint" 2>/dev/null)
    status_code="${response: -3}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ 通过${NC} (状态码: $status_code)"
        
        # 检查响应内容
        if [ -f /tmp/api_response.json ]; then
            response_size=$(wc -c < /tmp/api_response.json)
            if [ $response_size -gt 10 ]; then
                echo "    响应大小: ${response_size} 字节"
            fi
        fi
    else
        echo -e "${RED}❌ 失败${NC} (状态码: $status_code, 期望: $expected_status)"
        if [ -f /tmp/api_response.json ]; then
            echo "    响应内容: $(head -c 200 /tmp/api_response.json)"
        fi
    fi
}

# 检查服务是否运行
check_service() {
    local url=$1
    local name=$2
    
    echo -n "检查 $name ... "
    if curl -s "$url" >/dev/null 2>&1; then
        echo -e "${GREEN}✅ 运行中${NC}"
        return 0
    else
        echo -e "${RED}❌ 未运行${NC}"
        return 1
    fi
}

echo "🔍 检查外部服务状态..."
check_service "http://localhost:3001/health" "JavBus API (3001)"
check_service "http://localhost:3002/health" "JavSP包装器 (3002)"
check_service "http://localhost:3003/health" "Javinizer包装器 (3003)"

echo ""
echo "🔍 检查主服务状态..."
if ! check_service "$BASE_URL/health" "主服务 (8080)"; then
    echo -e "${YELLOW}⚠️  主服务未运行，请先启动主服务${NC}"
    echo "启动命令: go run cmd/server/main.go"
    exit 1
fi

echo ""
echo "🧪 开始API端点测试..."

# 基础API测试
echo ""
echo "📋 基础API测试:"
test_api "/health" "健康检查"
test_api "/api/v1/status" "系统状态"

# JAV相关API测试
echo ""
echo "🎬 JAV API测试:"
test_api "/api/v1/jav/movie/$TEST_CODE" "获取影片详情"
test_api "/api/v1/jav/search?q=$TEST_CODE" "搜索影片"
test_api "/api/v1/jav/actress/$TEST_ACTRESS" "获取演员信息"
test_api "/api/v1/jav/magnets/$TEST_CODE" "获取磁力链接"

# 数据源API测试
echo ""
echo "🔄 数据源API测试:"
test_api "/api/v1/jav/sources" "获取数据源列表"
test_api "/api/v1/jav/sources/javbus/movie/$TEST_CODE" "JavBus数据源"
test_api "/api/v1/jav/sources/javsp/movie/$TEST_CODE" "JavSP数据源"
test_api "/api/v1/jav/sources/javinizer/movie/$TEST_CODE" "Javinizer数据源"

# 统计API测试
echo ""
echo "📊 统计API测试:"
test_api "/api/v1/jav/stats" "获取统计信息"
test_api "/api/v1/jav/stats/sources" "数据源统计"
test_api "/api/v1/jav/stats/performance" "性能统计"

# 管理API测试
echo ""
echo "⚙️  管理API测试:"
test_api "/api/v1/admin/config" "获取配置"
test_api "/api/v1/admin/scrapers" "获取爬虫状态"
test_api "/api/v1/admin/cache/clear" "清除缓存" 200

# 错误处理测试
echo ""
echo "🚫 错误处理测试:"
test_api "/api/v1/jav/movie/INVALID-CODE" "无效番号" 404
test_api "/api/v1/jav/actress/不存在的演员" "不存在的演员" 404
test_api "/api/v1/nonexistent" "不存在的端点" 404

echo ""
echo "=" * 50
echo -e "${GREEN}🎉 API测试完成！${NC}"

# 清理临时文件
rm -f /tmp/api_response.json

echo ""
echo "📝 测试报告已生成"
echo "如需详细测试，请运行: go run test_integration.go"