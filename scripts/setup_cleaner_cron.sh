#!/bin/bash
# Aria2智能清理器定时任务配置脚本

SCRIPT_DIR="/www/wwwroot/JAVAPI.COM/scripts"
LOG_DIR="/www/wwwroot/JAVAPI.COM/logs"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

show_usage() {
    echo "🔧 Aria2智能清理器定时任务配置"
    echo "================================"
    echo "用法: $0 <命令> [间隔]"
    echo ""
    echo "命令:"
    echo "  install [间隔]  - 安装定时任务"
    echo "  remove          - 移除定时任务"
    echo "  status          - 查看定时任务状态"
    echo "  log             - 查看定时任务日志"
    echo ""
    echo "间隔选项:"
    echo "  5min    - 每5分钟执行一次"
    echo "  10min   - 每10分钟执行一次（默认）"
    echo "  15min   - 每15分钟执行一次"
    echo "  30min   - 每30分钟执行一次"
    echo "  1hour   - 每小时执行一次"
    echo "  2hour   - 每2小时执行一次"
    echo ""
    echo "示例:"
    echo "  $0 install 5min     # 每5分钟清理一次"
    echo "  $0 install          # 使用默认10分钟间隔"
    echo "  $0 remove           # 移除定时任务"
    echo "  $0 status           # 查看当前状态"
}

get_cron_expression() {
    case "$1" in
        "5min")
            echo "*/5 * * * *"
            ;;
        "10min"|"")
            echo "*/10 * * * *"
            ;;
        "15min")
            echo "*/15 * * * *"
            ;;
        "30min")
            echo "*/30 * * * *"
            ;;
        "1hour")
            echo "0 * * * *"
            ;;
        "2hour")
            echo "0 */2 * * *"
            ;;
        *)
            echo "无效的间隔: $1"
            return 1
            ;;
    esac
}

install_cron() {
    local interval="${1:-10min}"
    local cron_expr=$(get_cron_expression "$interval")
    
    if [ $? -ne 0 ]; then
        echo "❌ $cron_expr"
        return 1
    fi
    
    echo "📅 安装Aria2智能清理器定时任务..."
    echo "⏰ 执行间隔: $interval ($cron_expr)"
    
    # 移除旧的定时任务（如果存在）
    remove_cron_silent
    
    # 添加新的定时任务
    (crontab -l 2>/dev/null; echo "# Aria2智能清理器 - $interval间隔"; echo "$cron_expr $SCRIPT_DIR/start_cleaner.sh once >> $LOG_DIR/cleaner_cron.log 2>&1") | crontab -
    
    if [ $? -eq 0 ]; then
        echo "✅ 定时任务安装成功！"
        echo "📋 任务详情:"
        echo "   间隔: $interval"
        echo "   表达式: $cron_expr"
        echo "   脚本: $SCRIPT_DIR/start_cleaner.sh once"
        echo "   日志: $LOG_DIR/cleaner_cron.log"
        echo ""
        echo "💡 提示:"
        echo "   - 使用 '$0 log' 查看执行日志"
        echo "   - 使用 '$0 status' 查看任务状态"
        echo "   - 使用 '$0 remove' 移除定时任务"
    else
        echo "❌ 定时任务安装失败！"
        return 1
    fi
}

remove_cron_silent() {
    # 静默移除，不显示输出
    crontab -l 2>/dev/null | grep -v "Aria2智能清理器" | grep -v "start_cleaner.sh" | crontab - 2>/dev/null
}

remove_cron() {
    echo "🗑️  移除Aria2智能清理器定时任务..."
    
    # 检查是否存在定时任务
    if crontab -l 2>/dev/null | grep -q "start_cleaner.sh"; then
        remove_cron_silent
        echo "✅ 定时任务已移除"
    else
        echo "⚠️  未找到相关定时任务"
    fi
}

show_status() {
    echo "📊 Aria2智能清理器定时任务状态"
    echo "================================"
    
    # 检查crontab中的任务
    local cron_task=$(crontab -l 2>/dev/null | grep "start_cleaner.sh")
    
    if [ -n "$cron_task" ]; then
        echo "✅ 定时任务: 已安装"
        echo "📋 任务详情:"
        echo "$cron_task" | while read line; do
            if [[ $line == \#* ]]; then
                echo "   描述: ${line#\# }"
            else
                local cron_expr=$(echo "$line" | awk '{print $1" "$2" "$3" "$4" "$5}')
                echo "   表达式: $cron_expr"
                echo "   脚本: $(echo "$line" | awk '{for(i=6;i<=NF;i++) printf "%s ", $i; print ""}')"
            fi
        done
    else
        echo "❌ 定时任务: 未安装"
    fi
    
    echo ""
    
    # 检查日志文件
    if [ -f "$LOG_DIR/cleaner_cron.log" ]; then
        echo "📄 日志文件: 存在"
        echo "   路径: $LOG_DIR/cleaner_cron.log"
        echo "   大小: $(du -h "$LOG_DIR/cleaner_cron.log" | cut -f1)"
        echo "   最后修改: $(stat -c %y "$LOG_DIR/cleaner_cron.log" 2>/dev/null | cut -d. -f1)"
        
        # 显示最近的执行记录
        echo ""
        echo "📈 最近执行记录 (最后5次):"
        tail -20 "$LOG_DIR/cleaner_cron.log" 2>/dev/null | grep -E "(清理完成|清理了)" | tail -5 | while read line; do
            echo "   $line"
        done
    else
        echo "📄 日志文件: 不存在"
    fi
    
    # 检查清理器脚本状态
    echo ""
    if [ -f "$SCRIPT_DIR/start_cleaner.sh" ]; then
        echo "🔧 清理器脚本: 存在"
        if [ -x "$SCRIPT_DIR/start_cleaner.sh" ]; then
            echo "   权限: 可执行 ✅"
        else
            echo "   权限: 不可执行 ❌"
        fi
    else
        echo "🔧 清理器脚本: 不存在 ❌"
    fi
}

show_log() {
    echo "📖 Aria2智能清理器定时任务日志"
    echo "================================"
    
    if [ -f "$LOG_DIR/cleaner_cron.log" ]; then
        echo "📄 日志文件: $LOG_DIR/cleaner_cron.log"
        echo "📊 文件大小: $(du -h "$LOG_DIR/cleaner_cron.log" | cut -f1)"
        echo ""
        echo "📈 实时日志 (按Ctrl+C退出):"
        echo "--------------------------------"
        tail -f "$LOG_DIR/cleaner_cron.log"
    else
        echo "❌ 日志文件不存在: $LOG_DIR/cleaner_cron.log"
        echo ""
        echo "💡 提示: 定时任务执行后会自动创建日志文件"
    fi
}

# 主程序
case "$1" in
    "install")
        install_cron "$2"
        ;;
    "remove")
        remove_cron
        ;;
    "status")
        show_status
        ;;
    "log")
        show_log
        ;;
    *)
        show_usage
        ;;
esac