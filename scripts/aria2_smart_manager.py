#!/usr/bin/env python3
"""
Aria2智能任务管理器
解决低速任务占用下载槽位的问题
"""

import json
import requests
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

# 配置
ARIA2_RPC_URL = "http://localhost:6800/jsonrpc"
ARIA2_SECRET = "aria2secret"  # 从config.yaml获取

# 性能阈值
MIN_SPEED_THRESHOLD = 50 * 1024  # 50KB/s 最低速度阈值
SLOW_TASK_TIMEOUT = 300  # 5分钟低速超时
MAX_CONCURRENT_DOWNLOADS = 15
OPTIMAL_CONCURRENT_DOWNLOADS = 10  # 最优并发数

class Aria2Manager:
    def __init__(self):
        self.session = requests.Session()
        self.slow_tasks = {}  # 记录低速任务的开始时间
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('/www/wwwroot/JAVAPI.COM/logs/aria2_manager.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def call_aria2(self, method: str, params: List[Any] = None) -> Dict:
        """调用aria2 RPC接口"""
        if params is None:
            params = []
        
        payload = {
            "jsonrpc": "2.0",
            "id": "1",
            "method": method,
            "params": [f"token:{ARIA2_SECRET}"] + params
        }
        
        try:
            response = self.session.post(ARIA2_RPC_URL, json=payload, timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            self.logger.error(f"Aria2 RPC调用失败: {e}")
            return {"error": str(e)}

    def get_active_downloads(self) -> List[Dict]:
        """获取活跃下载任务"""
        result = self.call_aria2("aria2.tellActive")
        if "result" in result:
            return result["result"]
        return []

    def get_waiting_downloads(self) -> List[Dict]:
        """获取等待中的下载任务"""
        result = self.call_aria2("aria2.tellWaiting", [0, 100])
        if "result" in result:
            return result["result"]
        return []

    def pause_task(self, gid: str) -> bool:
        """暂停任务"""
        result = self.call_aria2("aria2.pause", [gid])
        return "result" in result

    def unpause_task(self, gid: str) -> bool:
        """恢复任务"""
        result = self.call_aria2("aria2.unpause", [gid])
        return "result" in result

    def analyze_task_performance(self, tasks: List[Dict]) -> Dict:
        """分析任务性能"""
        fast_tasks = []
        slow_tasks = []
        medium_tasks = []
        
        for task in tasks:
            gid = task["gid"]
            download_speed = int(task.get("downloadSpeed", 0))
            progress = float(task.get("completedLength", 0)) / max(float(task.get("totalLength", 1)), 1) * 100
            
            task_info = {
                "gid": gid,
                "speed": download_speed,
                "progress": progress,
                "name": self.get_task_name(task)
            }
            
            if download_speed > 1024 * 1024:  # > 1MB/s
                fast_tasks.append(task_info)
            elif download_speed < MIN_SPEED_THRESHOLD:  # < 50KB/s
                slow_tasks.append(task_info)
            else:
                medium_tasks.append(task_info)
        
        return {
            "fast": fast_tasks,
            "medium": medium_tasks,
            "slow": slow_tasks,
            "total_active": len(tasks)
        }

    def get_task_name(self, task: Dict) -> str:
        """获取任务名称"""
        files = task.get("files", [])
        if files:
            path = files[0].get("path", "")
            return path.split("/")[-1] if path else "Unknown"
        return "Unknown"

    def manage_slow_tasks(self, analysis: Dict) -> None:
        """管理慢速任务"""
        current_time = datetime.now()
        actions_taken = []
        
        # 记录新的慢速任务
        for task in analysis["slow"]:
            gid = task["gid"]
            if gid not in self.slow_tasks:
                self.slow_tasks[gid] = current_time
                self.logger.info(f"检测到慢速任务: {task['name']} (速度: {task['speed']/1024:.1f} KB/s)")
        
        # 检查需要暂停的慢速任务
        tasks_to_pause = []
        for gid, start_time in list(self.slow_tasks.items()):
            if current_time - start_time > timedelta(seconds=SLOW_TASK_TIMEOUT):
                # 找到对应的任务信息
                task_info = next((t for t in analysis["slow"] if t["gid"] == gid), None)
                if task_info:
                    tasks_to_pause.append(task_info)
                    del self.slow_tasks[gid]
        
        # 暂停超时的慢速任务
        for task in tasks_to_pause:
            if self.pause_task(task["gid"]):
                actions_taken.append(f"暂停慢速任务: {task['name']}")
                self.logger.info(f"暂停慢速任务: {task['name']} (速度: {task['speed']/1024:.1f} KB/s, 进度: {task['progress']:.1f}%)")
        
        return actions_taken

    def optimize_queue(self) -> List[str]:
        """优化下载队列"""
        active_tasks = self.get_active_downloads()
        waiting_tasks = self.get_waiting_downloads()
        
        analysis = self.analyze_task_performance(active_tasks)
        actions = []
        
        self.logger.info(f"当前状态: 活跃任务 {analysis['total_active']}, 快速 {len(analysis['fast'])}, 中速 {len(analysis['medium'])}, 慢速 {len(analysis['slow'])}")
        
        # 管理慢速任务
        slow_actions = self.manage_slow_tasks(analysis)
        actions.extend(slow_actions)
        
        # 如果活跃任务少于最优数量，且有等待任务，则启动新任务
        if analysis['total_active'] < OPTIMAL_CONCURRENT_DOWNLOADS and waiting_tasks:
            tasks_to_start = min(
                OPTIMAL_CONCURRENT_DOWNLOADS - analysis['total_active'],
                len(waiting_tasks)
            )
            
            for i in range(tasks_to_start):
                task = waiting_tasks[i]
                if self.unpause_task(task["gid"]):
                    task_name = self.get_task_name(task)
                    actions.append(f"启动等待任务: {task_name}")
                    self.logger.info(f"启动等待任务: {task_name}")
        
        return actions

    def run_monitoring(self, interval: int = 60):
        """运行监控循环"""
        self.logger.info("Aria2智能管理器启动")
        
        while True:
            try:
                actions = self.optimize_queue()
                if actions:
                    self.logger.info(f"执行了 {len(actions)} 个优化操作")
                
                time.sleep(interval)
                
            except KeyboardInterrupt:
                self.logger.info("收到停止信号，退出监控")
                break
            except Exception as e:
                self.logger.error(f"监控循环出错: {e}")
                time.sleep(interval)

if __name__ == "__main__":
    manager = Aria2Manager()
    
    # 可以选择运行模式
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "once":
        # 单次优化
        actions = manager.optimize_queue()
        print(f"执行了 {len(actions)} 个优化操作:")
        for action in actions:
            print(f"  - {action}")
    else:
        # 持续监控
        manager.run_monitoring(interval=60)  # 每分钟检查一次