#!/bin/bash
# Aria2智能清理器启动脚本

SCRIPT_DIR="/www/wwwroot/JAVAPI.COM/scripts"
LOG_DIR="/www/wwwroot/JAVAPI.COM/logs"

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python3"
    exit 1
fi

# 检查requests库是否安装
python3 -c "import requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "安装Python依赖..."
    pip3 install requests
fi

cd "$SCRIPT_DIR"

case "$1" in
    "start")
        echo "🚀 启动Aria2智能清理器（监控模式）..."
        nohup python3 aria2_smart_cleaner.py monitor > "$LOG_DIR/cleaner.log" 2>&1 &
        echo $! > "$LOG_DIR/cleaner.pid"
        echo "✅ 清理器已启动，PID: $(cat $LOG_DIR/cleaner.pid)"
        echo "📋 日志文件: $LOG_DIR/cleaner.log"
        ;;
    "stop")
        if [ -f "$LOG_DIR/cleaner.pid" ]; then
            PID=$(cat "$LOG_DIR/cleaner.pid")
            if kill -0 "$PID" 2>/dev/null; then
                kill "$PID"
                rm -f "$LOG_DIR/cleaner.pid"
                echo "🛑 清理器已停止"
            else
                echo "⚠️  清理器进程不存在"
                rm -f "$LOG_DIR/cleaner.pid"
            fi
        else
            echo "⚠️  未找到清理器PID文件"
        fi
        ;;
    "status")
        if [ -f "$LOG_DIR/cleaner.pid" ]; then
            PID=$(cat "$LOG_DIR/cleaner.pid")
            if kill -0 "$PID" 2>/dev/null; then
                echo "✅ 清理器正在运行 (PID: $PID)"
            else
                echo "❌ 清理器未运行"
                rm -f "$LOG_DIR/cleaner.pid"
            fi
        else
            echo "❌ 清理器未运行"
        fi
        ;;
    "once")
        echo "🧹 执行单次清理..."
        python3 aria2_smart_cleaner.py once
        ;;
    "list")
        echo "📋 已清理的任务列表:"
        python3 aria2_smart_cleaner.py list
        ;;
    "disk")
        echo "💾 磁盘使用情况:"
        python3 aria2_smart_cleaner.py disk
        ;;
    "restart")
        if [ -z "$2" ]; then
            echo "❌ 请提供任务GID"
            echo "用法: $0 restart <GID>"
            exit 1
        fi
        echo "🔄 重新启动任务: $2"
        python3 aria2_smart_cleaner.py restart "$2"
        ;;
    "log")
        echo "📖 查看清理器日志:"
        tail -f "$LOG_DIR/cleaner.log"
        ;;
    *)
        echo "🔧 Aria2智能清理器管理脚本"
        echo "================================"
        echo "用法: $0 <命令> [参数]"
        echo ""
        echo "命令:"
        echo "  start    - 启动清理器（后台监控模式）"
        echo "  stop     - 停止清理器"
        echo "  status   - 查看清理器状态"
        echo "  once     - 执行单次清理"
        echo "  list     - 列出已清理的任务"
        echo "  disk     - 显示磁盘使用情况"
        echo "  restart <GID> - 重新启动指定任务"
        echo "  log      - 查看实时日志"
        echo ""
        echo "示例:"
        echo "  $0 start           # 启动后台监控"
        echo "  $0 once            # 立即清理一次"
        echo "  $0 list            # 查看已清理任务"
        echo "  $0 restart abc123  # 重新启动任务"
        ;;
esac