#!/bin/bash

# JAV完整采集器启动脚本
# 功能：智能磁力选择 + 数据库存储 + 图片下载 + aria2下载

echo "🚀 启动JAV完整采集器..."
echo "========================================"

# 切换到项目目录
cd /www/wwwroot/JAVAPI.COM

# 检查aria2是否运行
echo "🔌 检查aria2状态..."
if curl -s -X POST http://localhost:6800/jsonrpc -H "Content-Type: application/json" -d '{"jsonrpc":"2.0","method":"aria2.getVersion","id":"test","params":["token:aria2secret"]}' > /dev/null 2>&1; then
    echo "✅ aria2运行正常"
else
    echo "⚠️  aria2未运行，将跳过自动下载功能"
fi

# 检查数据库连接
echo "🗄️  检查数据库连接..."

# 启动完整采集器
echo "🎬 启动完整采集器..."
echo "功能："
echo "  ✅ 多源数据采集 (JavBus + Javinizer + JavSP)"
echo "  ✅ 智能磁力选择 (字幕优先 + 质量筛选)"
echo "  ✅ 完整图片下载 (封面 + 海报 + 演员头像)"
echo "  ✅ aria2自动下载"
echo "  ✅ 数据库存储"
echo ""

# 运行采集器
go run \
    cmd/complete_javbus_scraper/main.go \
    cmd/complete_javbus_scraper/helpers.go \
    cmd/complete_javbus_scraper/helpers_extended.go \
    cmd/complete_javbus_scraper/magnet_selector.go \
    cmd/complete_javbus_scraper/subtitle_selector.go

echo ""
echo "🏁 采集器已退出"