#!/bin/bash

# JAV API 快捷命令设置脚本

echo "🔧 设置JAV API快捷命令..."

# 添加到 ~/.bashrc
cat >> ~/.bashrc << 'EOF'

# JAV API 快捷命令
alias jav-scraper='cd /www/wwwroot/JAVAPI.COM && go run cmd/complete_javbus_scraper/main.go cmd/complete_javbus_scraper/helpers.go cmd/complete_javbus_scraper/helpers_extended.go cmd/complete_javbus_scraper/magnet_selector.go cmd/complete_javbus_scraper/subtitle_selector.go'
alias jav-server='cd /www/wwwroot/JAVAPI.COM && go run cmd/server/main.go'
alias jav-cd='cd /www/wwwroot/JAVAPI.COM'
alias jav-logs='cd /www/wwwroot/JAVAPI.COM && tail -f logs/*.log'

EOF

echo "✅ 快捷命令已添加到 ~/.bashrc"
echo ""
echo "🎯 可用命令："
echo "  jav-scraper  - 启动完整采集器"
echo "  jav-server   - 启动主服务器"
echo "  jav-cd       - 切换到项目目录"
echo "  jav-logs     - 查看日志"
echo ""
echo "💡 运行 'source ~/.bashrc' 或重新登录以生效"