#!/usr/bin/env python3
"""
115网盘磁力链接添加脚本 - 改进版
支持VNC环境下的操作
"""

import time
import sys
import subprocess
import os

def add_magnet_to_115_improved(magnet_url):
    """通过115客户端界面添加磁力链接 - 改进版"""
    
    print(f"准备添加磁力链接: {magnet_url}")
    
    # 设置DISPLAY环境变量
    os.environ['DISPLAY'] = ':1'
    
    # 检查115客户端是否运行
    try:
        result = subprocess.run(['pgrep', '-f', '115Browser'], capture_output=True, text=True)
        if not result.stdout.strip():
            print("115客户端未运行，正在启动...")
            env = os.environ.copy()
            env['DISPLAY'] = ':1'
            subprocess.Popen(['/usr/local/115Browser/115.sh'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL,
                           env=env)
            time.sleep(5)
    except Exception as e:
        print(f"检查115客户端状态失败: {e}")
    
    # 查找115客户端窗口
    try:
        # 查找所有窗口
        result = subprocess.run(['xdotool', 'search', '--onlyvisible', '--class', '115Browser'], 
                              capture_output=True, text=True, env={'DISPLAY': ':1'})
        
        if not result.stdout.strip():
            # 尝试其他方式查找窗口
            result = subprocess.run(['xdotool', 'search', '--name', '115'], 
                                  capture_output=True, text=True, env={'DISPLAY': ':1'})
        
        if result.stdout.strip():
            window_id = result.stdout.strip().split('\n')[0]
            print(f"找到115客户端窗口: {window_id}")
            
            # 激活窗口
            subprocess.run(['xdotool', 'windowactivate', window_id], 
                          env={'DISPLAY': ':1'})
            time.sleep(1)
            
            # 点击窗口确保焦点
            subprocess.run(['xdotool', 'windowfocus', window_id], 
                          env={'DISPLAY': ':1'})
            time.sleep(1)
            
            # 尝试多种方式打开添加任务对话框
            print("尝试打开添加任务对话框...")
            
            # 方法1: Ctrl+N (新建任务)
            subprocess.run(['xdotool', 'key', '--window', window_id, 'ctrl+n'], 
                          env={'DISPLAY': ':1'})
            time.sleep(2)
            
            # 方法2: Ctrl+A (添加任务)
            subprocess.run(['xdotool', 'key', '--window', window_id, 'ctrl+a'], 
                          env={'DISPLAY': ':1'})
            time.sleep(2)
            
            # 方法3: 尝试点击添加按钮的位置 (假设在左上角区域)
            subprocess.run(['xdotool', 'mousemove', '--window', window_id, '100', '100'], 
                          env={'DISPLAY': ':1'})
            subprocess.run(['xdotool', 'click', '1'], env={'DISPLAY': ':1'})
            time.sleep(2)
            
            # 输入磁力链接
            print("输入磁力链接...")
            subprocess.run(['xdotool', 'type', '--delay', '100', magnet_url], 
                          env={'DISPLAY': ':1'})
            time.sleep(1)
            
            # 按回车确认
            subprocess.run(['xdotool', 'key', 'Return'], env={'DISPLAY': ':1'})
            time.sleep(2)
            
            # 再次按回车以防需要二次确认
            subprocess.run(['xdotool', 'key', 'Return'], env={'DISPLAY': ':1'})
            time.sleep(1)
            
            print("磁力链接操作完成")
            return True
            
        else:
            print("未找到115客户端窗口")
            return False
            
    except Exception as e:
        print(f"窗口操作失败: {e}")
        return False

def check_115_offline_tasks():
    """检查115离线任务"""
    try:
        # 尝试通过网页版API检查任务
        result = subprocess.run(['curl', '-s', 'https://115.com/web/lixian/?ct=lixian&ac=task_lists'], 
                              capture_output=True, text=True, timeout=10)
        if "embz-325" in result.stdout:
            print("✅ 在任务列表中找到了该磁力链接任务")
            return True
    except:
        pass
    
    print("⚠️  无法验证任务是否添加成功，请手动检查115客户端")
    return False

def main():
    if len(sys.argv) != 2:
        print("用法: python3 add_115_magnet_improved.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    
    if not magnet_url.startswith("magnet:"):
        print("错误: 请提供有效的磁力链接")
        sys.exit(1)
    
    success = add_magnet_to_115_improved(magnet_url)
    
    if success:
        print("操作完成！正在验证任务是否添加成功...")
        time.sleep(3)
        check_115_offline_tasks()
    else:
        print("操作失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()