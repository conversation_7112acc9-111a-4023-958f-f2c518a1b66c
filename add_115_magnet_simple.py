#!/usr/bin/env python3
"""
115网盘磁力链接添加脚本 - 简化版
直接使用115客户端的界面操作
"""

import time
import sys
import subprocess
import os

def add_magnet_to_115_simple(magnet_url):
    """通过115客户端界面添加磁力链接"""
    
    print(f"准备添加磁力链接: {magnet_url}")
    
    # 检查115客户端是否运行
    try:
        result = subprocess.run(['pgrep', '-f', '115Browser'], capture_output=True, text=True)
        if not result.stdout.strip():
            print("115客户端未运行，正在启动...")
            subprocess.Popen(['/usr/local/115Browser/115.sh'], 
                           stdout=subprocess.DEVNULL, 
                           stderr=subprocess.DEVNULL)
            time.sleep(5)
    except Exception as e:
        print(f"检查115客户端状态失败: {e}")
    
    # 使用xdotool模拟键盘操作
    try:
        # 激活115客户端窗口
        subprocess.run(['xdotool', 'search', '--name', '115', 'windowactivate'], 
                      capture_output=True)
        time.sleep(1)
        
        # 使用快捷键打开添加任务对话框 (通常是Ctrl+N或Ctrl+A)
        subprocess.run(['xdotool', 'key', 'ctrl+n'], capture_output=True)
        time.sleep(2)
        
        # 如果Ctrl+N不行，尝试Ctrl+A
        subprocess.run(['xdotool', 'key', 'ctrl+a'], capture_output=True)
        time.sleep(2)
        
        # 输入磁力链接
        subprocess.run(['xdotool', 'type', magnet_url], capture_output=True)
        time.sleep(1)
        
        # 按回车确认
        subprocess.run(['xdotool', 'key', 'Return'], capture_output=True)
        time.sleep(2)
        
        print("磁力链接已通过键盘操作添加到115客户端")
        return True
        
    except Exception as e:
        print(f"键盘操作失败: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("用法: python3 add_115_magnet_simple.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    
    if not magnet_url.startswith("magnet:"):
        print("错误: 请提供有效的磁力链接")
        sys.exit(1)
    
    # 检查xdotool是否安装
    try:
        subprocess.run(['which', 'xdotool'], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("正在安装xdotool...")
        subprocess.run(['apt', 'install', '-y', 'xdotool'], check=True)
    
    success = add_magnet_to_115_simple(magnet_url)
    
    if success:
        print("操作完成！请检查115客户端是否成功添加了磁力链接。")
        sys.exit(0)
    else:
        print("操作失败！")
        sys.exit(1)

if __name__ == "__main__":
    main()