#!/usr/bin/env python3
"""
115网盘简单登录工具
"""

import requests
import json
import time
import os

def get_qr_and_login():
    """获取二维码并等待登录"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    })
    
    print("🔐 115网盘登录")
    print("=" * 50)
    
    # 获取二维码
    try:
        response = session.get('https://qrcodeapi.115.com/api/1.0/web/1.0/token/', timeout=10)
        data = response.json()
        
        if not data.get('state'):
            print("❌ 获取二维码失败")
            return False
        
        uid = data['data']['uid']
        qr_url = data['data']['qrcode']
        
        print(f"📱 请使用115手机APP扫描以下二维码:")
        print(f"🔗 {qr_url}")
        print(f"🆔 UID: {uid}")
        print("\n⏳ 等待扫码中... (2分钟有效期)")
        
        # 等待扫码
        check_url = f"https://qrcodeapi.115.com/get/status/?uid={uid}"
        
        for i in range(120):  # 2分钟
            try:
                check_response = session.get(check_url, timeout=5)
                check_data = check_response.json()
                status = check_data.get('data', {}).get('status', 0)
                
                if status == 2:
                    print("\n✅ 扫码成功！正在完成登录...")
                    
                    # 完成登录
                    login_url = f"https://passportapi.115.com/app/1.0/web/1.0/login/qrcode/?account={uid}"
                    login_response = session.post(login_url, timeout=10)
                    login_data = login_response.json()
                    
                    if login_data.get('state'):
                        # 保存cookies
                        cookies_file = '/www/wwwroot/JAVAPI.COM/115_cookies.json'
                        with open(cookies_file, 'w') as f:
                            json.dump(dict(session.cookies), f, indent=2)
                        
                        # 获取用户信息
                        user_response = session.get('https://my.115.com/?ct=guide&ac=status', timeout=10)
                        user_data = user_response.json()
                        
                        if user_data.get('state'):
                            user_info = user_data.get('data', {})
                            print(f"🎉 登录成功！")
                            print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
                            print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
                            print(f"💾 登录信息已保存到: {cookies_file}")
                            return True
                
                elif status == -1:
                    print("\n❌ 二维码已过期")
                    return False
                
            except Exception as e:
                if i % 20 == 0:
                    print(f"检查状态出错: {e}")
            
            if i % 10 == 0 and i > 0:
                remaining = 120 - i
                print(f"⏰ 剩余时间: {remaining}秒")
            
            time.sleep(1)
        
        print("\n⏰ 等待超时")
        return False
        
    except Exception as e:
        print(f"❌ 登录过程出错: {e}")
        return False

def check_login():
    """检查登录状态"""
    cookies_file = '/www/wwwroot/JAVAPI.COM/115_cookies.json'
    
    if not os.path.exists(cookies_file):
        print("❌ 未找到登录信息")
        return False
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        })
        
        # 加载cookies
        with open(cookies_file, 'r') as f:
            cookies = json.load(f)
            session.cookies.update(cookies)
        
        # 检查登录状态
        response = session.get('https://my.115.com/?ct=guide&ac=status', timeout=10)
        data = response.json()
        
        if data.get('state'):
            user_info = data.get('data', {})
            print("✅ 已登录")
            print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
            print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
            return True
        else:
            print("❌ 登录已过期")
            return False
            
    except Exception as e:
        print(f"❌ 检查登录状态失败: {e}")
        return False

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) < 2:
        print("115网盘登录工具")
        print("用法:")
        print("  python3 115_simple_login.py login   # 二维码登录")
        print("  python3 115_simple_login.py status  # 检查登录状态")
    elif sys.argv[1] == 'login':
        get_qr_and_login()
    elif sys.argv[1] == 'status':
        check_login()
    else:
        print(f"未知命令: {sys.argv[1]}")