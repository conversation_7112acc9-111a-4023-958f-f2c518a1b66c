# Aria2智能优化配置
# 专门解决低速任务问题

## 文件保存设置 ##
dir=/downloads
disk-cache=256M
file-allocation=falloc
no-file-allocation-limit=64M
continue=true
always-resume=true
max-resume-failure-tries=3
remote-time=true

## 进度保存设置 ##
input-file=/config/aria2.session
save-session=/config/aria2.session
save-session-interval=60
auto-save-interval=300
force-save=false

## 智能队列管理 - 核心优化 ##
max-concurrent-downloads=10   # 降低到10个，确保质量
max-connection-per-server=16  # 每个任务更多连接
min-split-size=20M           # 大分片，减少管理开销
split=16                     # 16个连接分片

## 速度控制 - 关键配置 ##
max-download-limit=0         # 不限制总速度
lowest-speed-limit=50K       # 最低速度50KB/s，低于此速度会暂停
max-tries=3                  # 减少重试，快速释放慢速资源
retry-wait=10                # 重试间隔10秒
connect-timeout=15           # 连接超时15秒
timeout=60                   # 总超时60秒

## BT/磁力优化 ##
bt-max-peers=80              # 减少peer数量，专注质量连接
bt-request-peer-speed-limit=50K  # peer最低速度50KB/s
dht-listen-port=6881-6999
enable-dht=true
enable-dht6=false
enable-peer-exchange=true
seed-ratio=0.0               # 不做种，下载完立即停止
seed-time=0                  # 0秒做种时间
bt-stop-timeout=30
bt-tracker-timeout=10
bt-tracker-connect-timeout=10

## 内存和性能优化 ##
max-overall-upload-limit=1K   # 限制上传，专注下载
bt-max-open-files=100
enable-mmap=true
max-file-not-found=3
piece-length=1M              # 1MB分片大小
min-tls-version=TLSv1.2

## 自动管理 ##
auto-file-renaming=true
parameterized-uri=true
reuse-uri=true
max-connection-per-server=16
max-file-not-found=3

## 日志设置 ##
log-level=notice
console-log-level=notice
quiet=false

## RPC设置 ##
enable-rpc=true
rpc-listen-all=true
rpc-listen-port=6800
rpc-secret=aria2secret
rpc-max-request-size=10M

## 高级优化 ##
# 这些设置帮助快速识别和处理慢速连接
check-integrity=true
checksum=md5
realtime-chunk-checksum=true
max-overall-download-limit=0
max-download-result=120
download-result=default
user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36