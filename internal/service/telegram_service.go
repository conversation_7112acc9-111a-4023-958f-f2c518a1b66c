package service

import (
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/pkg/logger"
	"magnet-downloader/pkg/telegram"
)

// TelegramService Telegram通知服务接口
type TelegramService interface {
	// 发送采集完成通知
	SendScrapingCompleted(stats *telegram.ScrapingStats) error
	// 发送采集失败通知
	SendScrapingFailed(errorMsg string) error
	// 发送上传完成通知
	SendUploadCompleted(stats *telegram.UploadStats) error
	// 发送上传失败通知
	SendUploadFailed(fileName, provider, errorMsg string) error
	// 发送上传进度通知
	SendUploadProgress(fileName string, progress float64, provider string) error
	// 发送系统错误通知
	SendSystemError(component, errorType, errorMsg string) error
	// 检查服务是否可用
	IsEnabled() bool
}

// telegramService Telegram通知服务实现
type telegramService struct {
	client *telegram.Client
	config *config.TelegramConfig
}

// NewTelegramService 创建Telegram通知服务
func NewTelegramService(cfg *config.TelegramConfig) TelegramService {
	if cfg == nil || !cfg.Enabled {
		logger.Infof("Telegram通知服务已禁用")
		return &telegramService{
			client: nil,
			config: cfg,
		}
	}

	// 转换配置
	telegramConfig := &telegram.Config{
		Enabled:   cfg.Enabled,
		BotToken:  cfg.BotToken,
		ChatID:    cfg.ChatID,
		MaxRetries: cfg.MaxRetries,
		Notifications: telegram.NotificationConfig{
			ScrapingProgress:  cfg.Notifications.ScrapingProgress,
			ScrapingCompleted: cfg.Notifications.ScrapingCompleted,
			ScrapingFailed:    cfg.Notifications.ScrapingFailed,
			UploadProgress:    cfg.Notifications.UploadProgress,
			UploadCompleted:   cfg.Notifications.UploadCompleted,
			UploadFailed:      cfg.Notifications.UploadFailed,
			SystemError:       cfg.Notifications.SystemError,
		},
	}

	// 解析超时时间
	if cfg.Timeout != "" {
		if timeout, err := time.ParseDuration(cfg.Timeout); err == nil {
			telegramConfig.Timeout = timeout
		} else {
			logger.Warnf("解析Telegram超时时间失败: %v, 使用默认值", err)
			telegramConfig.Timeout = 30 * time.Second
		}
	} else {
		telegramConfig.Timeout = 30 * time.Second
	}

	client := telegram.NewClient(telegramConfig)
	
	return &telegramService{
		client: client,
		config: cfg,
	}
}

// SendScrapingCompleted 发送采集完成通知
func (s *telegramService) SendScrapingCompleted(stats *telegram.ScrapingStats) error {
	if !s.IsEnabled() || !s.config.Notifications.ScrapingCompleted {
		return nil
	}

	message := telegram.FormatScrapingCompleted(stats)
	return s.client.SendMessage(message)
}

// SendScrapingFailed 发送采集失败通知
func (s *telegramService) SendScrapingFailed(errorMsg string) error {
	if !s.IsEnabled() || !s.config.Notifications.ScrapingFailed {
		return nil
	}

	message := "❌ *JAV采集任务失败*\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
	message += "💥 错误信息: `" + errorMsg + "`\n"
	message += "🕐 失败时间: `" + time.Now().Format("2006-01-02 15:04:05") + "`"

	return s.client.SendMessage(message)
}

// SendUploadCompleted 发送上传完成通知
func (s *telegramService) SendUploadCompleted(stats *telegram.UploadStats) error {
	if !s.IsEnabled() || !s.config.Notifications.UploadCompleted {
		return nil
	}

	message := telegram.FormatUploadCompleted(stats)
	return s.client.SendMessage(message)
}

// SendUploadFailed 发送上传失败通知
func (s *telegramService) SendUploadFailed(fileName, provider, errorMsg string) error {
	if !s.IsEnabled() || !s.config.Notifications.UploadFailed {
		return nil
	}

	message := telegram.FormatUploadFailed(fileName, provider, errorMsg)
	return s.client.SendMessage(message)
}

// SendUploadProgress 发送上传进度通知
func (s *telegramService) SendUploadProgress(fileName string, progress float64, provider string) error {
	if !s.IsEnabled() || !s.config.Notifications.UploadProgress {
		return nil
	}

	message := telegram.FormatUploadProgress(fileName, progress, provider)
	return s.client.SendMessage(message)
}

// SendSystemError 发送系统错误通知
func (s *telegramService) SendSystemError(component, errorType, errorMsg string) error {
	if !s.IsEnabled() || !s.config.Notifications.SystemError {
		return nil
	}

	message := "🚨 *系统错误通知*\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n"
	message += "🔧 组件: `" + component + "`\n"
	message += "⚠️ 错误类型: `" + errorType + "`\n"
	message += "💥 错误信息: `" + errorMsg + "`\n"
	message += "🕐 发生时间: `" + time.Now().Format("2006-01-02 15:04:05") + "`"

	return s.client.SendMessage(message)
}

// IsEnabled 检查服务是否可用
func (s *telegramService) IsEnabled() bool {
	return s.client != nil && s.config != nil && s.config.Enabled
}