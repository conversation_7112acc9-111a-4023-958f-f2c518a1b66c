package model

import (
	"time"

	"gorm.io/gorm"
)

// JAVSimilarMovie JAV相关影片模型
type JAVSimilarMovie struct {
	ID              uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	MovieID         uint           `json:"movie_id" gorm:"not null;index;comment:主影片ID"`
	SimilarMovieID  uint           `json:"similar_movie_id" gorm:"not null;index;comment:相关影片ID"`
	Similarity      float64        `json:"similarity" gorm:"type:decimal(5,4);default:0;comment:相似度(0-1)"`
	RecommendReason string         `json:"recommend_reason" gorm:"type:varchar(200);comment:推荐理由"`
	CreatedAt       time.Time      `json:"created_at" gorm:"autoCreateTime;comment:创建时间"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"autoUpdateTime;comment:更新时间"`
	DeletedAt       gorm.DeletedAt `json:"deleted_at" gorm:"index;comment:删除时间"`

	// 关联关系
	Movie        JAVMovie `json:"movie,omitempty" gorm:"foreignKey:MovieID;references:ID"`
	SimilarMovie JAVMovie `json:"similar_movie,omitempty" gorm:"foreignKey:SimilarMovieID;references:ID"`
}

// TableName 指定表名
func (JAVSimilarMovie) TableName() string {
	return "jav_similar_movies"
}

// JAVSimilarMovieProfile JAV相关影片资料（用于API响应）
type JAVSimilarMovieProfile struct {
	ID              uint    `json:"id"`
	MovieID         uint    `json:"movie_id"`
	SimilarMovieID  uint    `json:"similar_movie_id"`
	Similarity      float64 `json:"similarity"`
	RecommendReason string  `json:"recommend_reason"`
	Movie           struct {
		ID       uint   `json:"id"`
		Code     string `json:"code"`
		Title    string `json:"title"`
		CoverURL string `json:"cover_url"`
	} `json:"movie"`
	SimilarMovie struct {
		ID       uint   `json:"id"`
		Code     string `json:"code"`
		Title    string `json:"title"`
		CoverURL string `json:"cover_url"`
	} `json:"similar_movie"`
	CreatedAt time.Time `json:"created_at"`
}