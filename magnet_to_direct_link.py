#!/usr/bin/env python3
"""
磁力链接转直链工具
基于webtor.io API实现
"""

import requests
import time
import subprocess
import re
import json
import sys
from urllib.parse import quote
import threading

class MagnetToDirectLink:
    def __init__(self):
        self.webtor_base_url = "https://webtor.io"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.last_clipboard = ""
        
    def get_clipboard_content(self):
        """获取剪贴板内容"""
        try:
            # 设置DISPLAY环境变量
            env = {'DISPLAY': ':1'}
            result = subprocess.run(['xclip', '-selection', 'clipboard', '-o'], 
                                  capture_output=True, text=True, env=env)
            return result.stdout.strip()
        except Exception as e:
            print(f"获取剪贴板内容失败: {e}")
            return ""
    
    def is_magnet_link(self, text):
        """检查是否为磁力链接"""
        return text.startswith('magnet:?xt=urn:btih:')
    
    def extract_hash_from_magnet(self, magnet_url):
        """从磁力链接中提取hash"""
        match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
        if match:
            return match.group(1)
        return None
    
    def get_torrent_info_via_webtor(self, magnet_url):
        """通过webtor.io获取torrent信息"""
        try:
            print("🔍 正在通过webtor.io解析磁力链接...")
            
            # 方法1: 直接访问webtor.io的magnet处理页面
            encoded_magnet = quote(magnet_url)
            webtor_url = f"{self.webtor_base_url}/?magnet={encoded_magnet}"
            
            print(f"📡 访问: {webtor_url}")
            
            response = self.session.get(webtor_url, timeout=30)
            
            if response.status_code == 200:
                print("✅ 成功访问webtor.io")
                return {
                    'success': True,
                    'webtor_url': webtor_url,
                    'magnet': magnet_url
                }
            else:
                print(f"❌ webtor.io返回状态码: {response.status_code}")
                return {'success': False, 'error': f'HTTP {response.status_code}'}
                
        except Exception as e:
            print(f"❌ webtor.io API调用失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_direct_links_alternative(self, magnet_url):
        """使用替代方法获取直链"""
        try:
            print("🔄 尝试替代方法...")
            
            # 方法1: 使用公共API服务
            apis = [
                {
                    'name': 'Webtor.io直接访问',
                    'url': f"https://webtor.io/?magnet={quote(magnet_url)}",
                    'type': 'web'
                },
                {
                    'name': 'Instant.io',
                    'url': f"https://instant.io/#{magnet_url}",
                    'type': 'web'
                }
            ]
            
            results = []
            for api in apis:
                try:
                    print(f"📡 尝试 {api['name']}...")
                    if api['type'] == 'web':
                        results.append({
                            'service': api['name'],
                            'url': api['url'],
                            'type': 'web_interface'
                        })
                except Exception as e:
                    print(f"❌ {api['name']} 失败: {e}")
            
            return results
            
        except Exception as e:
            print(f"❌ 替代方法失败: {e}")
            return []
    
    def process_magnet_link(self, magnet_url):
        """处理磁力链接"""
        print("=" * 60)
        print("🧲 检测到磁力链接!")
        print("=" * 60)
        print(f"磁力链接: {magnet_url[:80]}...")
        
        # 提取hash
        torrent_hash = self.extract_hash_from_magnet(magnet_url)
        if torrent_hash:
            print(f"📋 Torrent Hash: {torrent_hash}")
        
        # 尝试通过webtor.io获取信息
        webtor_result = self.get_torrent_info_via_webtor(magnet_url)
        
        if webtor_result.get('success'):
            print("\n✅ Webtor.io 处理成功!")
            print(f"🌐 在线播放/下载: {webtor_result['webtor_url']}")
        
        # 获取替代链接
        alternative_links = self.get_direct_links_alternative(magnet_url)
        
        if alternative_links:
            print("\n🔗 可用的在线服务:")
            for i, link in enumerate(alternative_links, 1):
                print(f"{i}. {link['service']}: {link['url']}")
        
        # 提供手动操作指导
        print("\n" + "=" * 60)
        print("📖 手动操作指导:")
        print("=" * 60)
        print("1. 复制上面的链接到浏览器打开")
        print("2. 等待torrent解析完成")
        print("3. 选择要下载的文件")
        print("4. 获取直接下载链接")
        print("=" * 60)
        
        return True
    
    def monitor_clipboard(self):
        """监听剪贴板变化"""
        print("🎯 开始监听剪贴板中的磁力链接...")
        print("💡 复制磁力链接到剪贴板即可自动处理")
        print("🛑 按 Ctrl+C 退出")
        print("-" * 50)
        
        while True:
            try:
                current_clipboard = self.get_clipboard_content()
                
                if current_clipboard and current_clipboard != self.last_clipboard:
                    if self.is_magnet_link(current_clipboard):
                        self.process_magnet_link(current_clipboard)
                    
                    self.last_clipboard = current_clipboard
                
                time.sleep(1)  # 每秒检查一次
                
            except KeyboardInterrupt:
                print("\n👋 程序已退出")
                break
            except Exception as e:
                print(f"❌ 监听过程中出错: {e}")
                time.sleep(5)

def main():
    print("🚀 磁力链接转直链工具")
    print("基于 webtor.io 和其他在线服务")
    print("=" * 50)
    
    # 检查依赖
    try:
        subprocess.run(['which', 'xclip'], check=True, capture_output=True)
    except subprocess.CalledProcessError:
        print("❌ 未找到 xclip，正在安装...")
        subprocess.run(['apt', 'install', '-y', 'xclip'], check=True)
    
    if len(sys.argv) > 1:
        # 直接处理命令行参数中的磁力链接
        magnet_url = sys.argv[1]
        if not magnet_url.startswith('magnet:'):
            print("❌ 请提供有效的磁力链接")
            sys.exit(1)
        
        converter = MagnetToDirectLink()
        converter.process_magnet_link(magnet_url)
    else:
        # 监听剪贴板模式
        converter = MagnetToDirectLink()
        converter.monitor_clipboard()

if __name__ == "__main__":
    main()