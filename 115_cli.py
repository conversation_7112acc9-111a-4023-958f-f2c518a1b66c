#!/usr/bin/env python3
"""
115网盘命令行工具
由于官方Linux客户端下载链接失效，使用API方式操作115网盘
"""

import requests
import json
import os
import sys
from urllib.parse import urlencode

class Pan115:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.cookies_file = '/tmp/115_cookies.json'
        self.load_cookies()
    
    def load_cookies(self):
        """加载保存的cookies"""
        if os.path.exists(self.cookies_file):
            try:
                with open(self.cookies_file, 'r') as f:
                    cookies = json.load(f)
                    self.session.cookies.update(cookies)
                print("已加载保存的登录信息")
            except:
                pass
    
    def save_cookies(self):
        """保存cookies"""
        with open(self.cookies_file, 'w') as f:
            json.dump(dict(self.session.cookies), f)
    
    def login_qr(self):
        """二维码登录"""
        print("正在获取二维码...")
        
        # 获取二维码
        qr_url = "https://qrcodeapi.115.com/api/1.0/web/1.0/token/"
        response = self.session.get(qr_url)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('state'):
                qr_code = data['data']['qrcode']
                uid = data['data']['uid']
                
                print(f"请使用115手机APP扫描以下二维码登录:")
                print(f"二维码链接: {qr_code}")
                print(f"或访问: https://qrcodeapi.115.com/get/uid/{uid}/")
                
                # 等待扫码
                return self.wait_for_scan(uid)
        
        return False
    
    def wait_for_scan(self, uid):
        """等待扫码结果"""
        import time
        
        check_url = f"https://qrcodeapi.115.com/get/status/?uid={uid}"
        
        print("等待扫码中...")
        for i in range(60):  # 等待60秒
            time.sleep(1)
            response = self.session.get(check_url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('data', {}).get('status') == 2:
                    # 登录成功
                    print("扫码成功！正在获取登录信息...")
                    
                    # 获取登录cookie
                    login_url = f"https://passportapi.115.com/app/1.0/web/1.0/login/qrcode/?account={uid}"
                    login_response = self.session.post(login_url)
                    
                    if login_response.status_code == 200:
                        login_data = login_response.json()
                        if login_data.get('state'):
                            self.save_cookies()
                            print("登录成功！")
                            return True
                
                elif data.get('data', {}).get('status') == -1:
                    print("二维码已过期")
                    return False
            
            if i % 5 == 0:
                print(f"等待扫码中... ({60-i}秒)")
        
        print("等待超时")
        return False
    
    def check_login(self):
        """检查登录状态"""
        url = "https://my.115.com/?ct=guide&ac=status"
        response = self.session.get(url)
        
        if response.status_code == 200:
            try:
                data = response.json()
                if data.get('state'):
                    user_info = data.get('data', {})
                    print(f"已登录用户: {user_info.get('user_name', 'Unknown')}")
                    return True
            except:
                pass
        
        print("未登录或登录已过期")
        return False
    
    def list_files(self, folder_id='0'):
        """列出文件"""
        if not self.check_login():
            return False
        
        url = f"https://webapi.115.com/files?aid=1&cid={folder_id}&o=user_ptime&asc=0&offset=0&show_dir=1&limit=56&code=&scid=&snap=0&natsort=1&record_open_time=1&source=&format=json"
        
        response = self.session.get(url)
        if response.status_code == 200:
            data = response.json()
            if data.get('state'):
                files = data.get('data', [])
                print(f"\n文件夹内容 (ID: {folder_id}):")
                print("-" * 60)
                
                for file in files:
                    file_type = "📁" if file.get('fid') else "📄"
                    name = file.get('n', 'Unknown')
                    size = file.get('s', 0)
                    
                    if file.get('fid'):  # 文件夹
                        print(f"{file_type} {name}/")
                    else:  # 文件
                        size_str = self.format_size(int(size)) if size else "0B"
                        print(f"{file_type} {name} ({size_str})")
                
                return True
        
        print("获取文件列表失败")
        return False
    
    def format_size(self, size):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size < 1024.0:
                return f"{size:.1f}{unit}"
            size /= 1024.0
        return f"{size:.1f}PB"

def main():
    pan = Pan115()
    
    if len(sys.argv) < 2:
        print("115网盘命令行工具")
        print("用法:")
        print("  python3 115_cli.py login    # 二维码登录")
        print("  python3 115_cli.py status   # 检查登录状态")
        print("  python3 115_cli.py list     # 列出根目录文件")
        print("  python3 115_cli.py list <folder_id>  # 列出指定文件夹")
        return
    
    command = sys.argv[1]
    
    if command == 'login':
        pan.login_qr()
    elif command == 'status':
        pan.check_login()
    elif command == 'list':
        folder_id = sys.argv[2] if len(sys.argv) > 2 else '0'
        pan.list_files(folder_id)
    else:
        print(f"未知命令: {command}")

if __name__ == '__main__':
    main()