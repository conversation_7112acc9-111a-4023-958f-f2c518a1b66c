#!/usr/bin/env python3
"""
115网盘网页登录工具
"""

import requests
import json
import os
import getpass

def web_login():
    """网页登录方式"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://115.com/',
        'Origin': 'https://115.com'
    })
    
    print("🔐 115网盘网页登录")
    print("=" * 50)
    
    # 获取用户输入
    username = input("📧 请输入用户名/邮箱: ").strip()
    password = getpass.getpass("🔑 请输入密码: ")
    
    if not username or not password:
        print("❌ 用户名和密码不能为空")
        return False
    
    try:
        # 首先访问登录页面获取必要的参数
        login_page = session.get('https://passport.115.com/?ct=login&ac=qrcode', timeout=10)
        
        # 尝试登录
        login_data = {
            'login[account]': username,
            'login[passwd]': password,
            'login[remember]': '1'
        }
        
        login_response = session.post(
            'https://passport.115.com/?ct=login&ac=ajax&is_ssl=1',
            data=login_data,
            timeout=10
        )
        
        if login_response.status_code == 200:
            result = login_response.json()
            
            if result.get('state'):
                print("✅ 登录成功！")
                
                # 保存cookies
                cookies_file = '/www/wwwroot/JAVAPI.COM/115_cookies.json'
                with open(cookies_file, 'w') as f:
                    json.dump(dict(session.cookies), f, indent=2)
                
                # 获取用户信息
                user_response = session.get('https://my.115.com/?ct=guide&ac=status', timeout=10)
                if user_response.status_code == 200:
                    user_data = user_response.json()
                    if user_data.get('state'):
                        user_info = user_data.get('data', {})
                        print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
                        print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
                
                print(f"💾 登录信息已保存到: {cookies_file}")
                return True
            else:
                error_msg = result.get('msg', '登录失败')
                print(f"❌ 登录失败: {error_msg}")
                return False
        else:
            print(f"❌ 登录请求失败: HTTP {login_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 登录过程出错: {e}")
        return False

def show_qr_login():
    """显示二维码登录信息"""
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        })
        
        response = session.get('https://qrcodeapi.115.com/api/1.0/web/1.0/token/', timeout=10)
        data = response.json()
        
        if data.get('state'):
            qr_url = data['data']['qrcode']
            uid = data['data']['uid']
            
            print("📱 二维码登录方式:")
            print(f"🔗 二维码链接: {qr_url}")
            print(f"🆔 UID: {uid}")
            print("⏰ 有效期: 2分钟")
            print("\n💡 请使用115手机APP扫描二维码")
            print("💡 或者复制链接到浏览器中打开扫码")
            return True
        else:
            print("❌ 获取二维码失败")
            return False
            
    except Exception as e:
        print(f"❌ 获取二维码出错: {e}")
        return False

def check_login():
    """检查登录状态"""
    cookies_file = '/www/wwwroot/JAVAPI.COM/115_cookies.json'
    
    if not os.path.exists(cookies_file):
        print("❌ 未找到登录信息")
        return False
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        })
        
        # 加载cookies
        with open(cookies_file, 'r') as f:
            cookies = json.load(f)
            session.cookies.update(cookies)
        
        # 检查登录状态
        response = session.get('https://my.115.com/?ct=guide&ac=status', timeout=10)
        data = response.json()
        
        if data.get('state'):
            user_info = data.get('data', {})
            print("✅ 已登录")
            print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
            print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
            return True
        else:
            print("❌ 登录已过期")
            return False
            
    except Exception as e:
        print(f"❌ 检查登录状态失败: {e}")
        return False

if __name__ == '__main__':
    import sys
    
    if len(sys.argv) < 2:
        print("115网盘登录工具")
        print("用法:")
        print("  python3 115_web_login.py web     # 用户名密码登录")
        print("  python3 115_web_login.py qr      # 显示二维码登录信息")
        print("  python3 115_web_login.py status  # 检查登录状态")
    elif sys.argv[1] == 'web':
        web_login()
    elif sys.argv[1] == 'qr':
        show_qr_login()
    elif sys.argv[1] == 'status':
        check_login()
    else:
        print(f"未知命令: {sys.argv[1]}")