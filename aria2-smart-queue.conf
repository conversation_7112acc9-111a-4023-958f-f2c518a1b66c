# 智能队列管理配置
# 解决低速任务占用资源问题

## 文件保存设置 ##
dir=/downloads
disk-cache=256M
file-allocation=falloc
no-file-allocation-limit=64M
continue=true
always-resume=true
max-resume-failure-tries=3
remote-time=true

## 进度保存设置 ##
input-file=/config/aria2.session
save-session=/config/aria2.session
save-session-interval=60
auto-save-interval=300
force-save=false

## 智能队列管理 ##
# 核心策略：少而精的并发，高效利用带宽
max-concurrent-downloads=15   # 降低到15个，确保每个任务有足够带宽
max-connection-per-server=16  # 增加单任务连接数
min-split-size=20M           # 增加分片大小，减少碎片
split=16                     # 每个任务16个连接

## 速度和超时优化 ##
max-download-limit=0         # 不限制总下载速度
lowest-speed-limit=100K      # 关键：设置最低速度限制100KB/s
max-tries=3
retry-wait=5
connect-timeout=15
timeout=30

## 自动队列管理 ##
# 当任务速度低于100KB/s超过5分钟时，aria2会自动暂停
auto-file-renaming=true
parameterized-uri=true

## BT/磁力链接优化 ##
bt-max-peers=100
bt-request-peer-speed-limit=100K  # BT对等体最低速度
dht-listen-port=6881-6999
enable-dht=true
enable-dht6=false
enable-peer-exchange=true
seed-ratio=0.1               # 降低做种比例，下载完成后快速停止
seed-time=10                 # 做种10分钟后停止
bt-stop-timeout=60
bt-tracker-timeout=10

## 内存和性能优化 ##
max-overall-upload-limit=1K   # 限制上传速度，专注下载
bt-max-open-files=100
disk-cache=256M
enable-mmap=true
max-file-not-found=3
max-tries=3

## 日志设置 ##
log-level=notice
console-log-level=notice