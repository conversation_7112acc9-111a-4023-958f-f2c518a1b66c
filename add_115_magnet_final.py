#!/usr/bin/env python3
"""
115网盘磁力链接添加脚本 - 最终版
使用wmctrl精确定位115窗口
"""

import time
import sys
import subprocess
import os
import re

def add_magnet_to_115_final(magnet_url):
    """通过115客户端界面添加磁力链接 - 最终版"""
    
    print(f"准备添加磁力链接: {magnet_url}")
    
    # 设置DISPLAY环境变量
    os.environ['DISPLAY'] = ':1'
    
    # 查找115窗口
    try:
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True, env={'DISPLAY': ':1'})
        
        window_id = None
        for line in result.stdout.split('\n'):
            if '115' in line:
                window_id = line.split()[0]
                print(f"找到115窗口: {window_id} - {line.strip()}")
                break
        
        if not window_id:
            print("未找到115客户端窗口")
            return False
        
        # 激活115窗口
        subprocess.run(['wmctrl', '-i', '-a', window_id], env={'DISPLAY': ':1'})
        time.sleep(2)
        
        print("115窗口已激活，开始操作...")
        
        # 方法1: 尝试使用快捷键
        print("尝试快捷键 Ctrl+Shift+N (新建离线任务)...")
        subprocess.run(['xdotool', 'key', 'ctrl+shift+n'], env={'DISPLAY': ':1'})
        time.sleep(3)
        
        # 方法2: 尝试其他快捷键
        print("尝试快捷键 Ctrl+N...")
        subprocess.run(['xdotool', 'key', 'ctrl+n'], env={'DISPLAY': ':1'})
        time.sleep(3)
        
        # 方法3: 点击页面上的添加按钮位置
        print("尝试点击添加按钮...")
        # 点击可能的添加按钮位置 (根据115界面布局)
        subprocess.run(['xdotool', 'mousemove', '400', '200'], env={'DISPLAY': ':1'})
        subprocess.run(['xdotool', 'click', '1'], env={'DISPLAY': ':1'})
        time.sleep(2)
        
        # 尝试点击其他可能的位置
        subprocess.run(['xdotool', 'mousemove', '100', '150'], env={'DISPLAY': ':1'})
        subprocess.run(['xdotool', 'click', '1'], env={'DISPLAY': ':1'})
        time.sleep(2)
        
        # 输入磁力链接
        print("输入磁力链接...")
        subprocess.run(['xdotool', 'type', '--delay', '50', magnet_url], env={'DISPLAY': ':1'})
        time.sleep(2)
        
        # 按回车确认
        print("按回车确认...")
        subprocess.run(['xdotool', 'key', 'Return'], env={'DISPLAY': ':1'})
        time.sleep(2)
        
        # 再次按回车以防需要二次确认
        subprocess.run(['xdotool', 'key', 'Return'], env={'DISPLAY': ':1'})
        time.sleep(1)
        
        # 尝试按Tab+Enter组合
        subprocess.run(['xdotool', 'key', 'Tab'], env={'DISPLAY': ':1'})
        time.sleep(1)
        subprocess.run(['xdotool', 'key', 'Return'], env={'DISPLAY': ':1'})
        time.sleep(1)
        
        print("磁力链接操作完成")
        return True
        
    except Exception as e:
        print(f"操作失败: {e}")
        return False

def manual_instructions():
    """提供手动操作指导"""
    print("\n" + "="*50)
    print("🔧 手动操作指导:")
    print("="*50)
    print("1. 在115客户端中点击 '云下载' 或 '离线下载' 标签")
    print("2. 点击 '添加任务' 或 '+' 按钮")
    print("3. 在弹出的对话框中粘贴磁力链接:")
    print(f"   magnet:?xt=urn:btih:9866D7F7256C520DE0A495F1945EEDDF1D3136B9&dn=embz-325")
    print("4. 点击 '确定' 或 '开始下载' 按钮")
    print("="*50)

def main():
    if len(sys.argv) != 2:
        print("用法: python3 add_115_magnet_final.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    
    if not magnet_url.startswith("magnet:"):
        print("错误: 请提供有效的磁力链接")
        sys.exit(1)
    
    success = add_magnet_to_115_final(magnet_url)
    
    if success:
        print("✅ 自动操作完成！")
        print("⚠️  请检查115客户端是否成功添加了磁力链接任务")
        print("如果没有成功，请参考下面的手动操作指导:")
        manual_instructions()
    else:
        print("❌ 自动操作失败！")
        manual_instructions()

if __name__ == "__main__":
    main()