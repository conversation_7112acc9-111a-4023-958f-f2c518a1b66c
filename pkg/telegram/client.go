package telegram

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"magnet-downloader/pkg/logger"
)

// Client Telegram客户端
type Client struct {
	config     *Config
	httpClient *http.Client
	baseURL    string
}

// NewClient 创建Telegram客户端
func NewClient(config *Config) *Client {
	if config == nil {
		config = DefaultConfig()
	}

	if err := config.Validate(); err != nil {
		logger.Errorf("Telegram配置验证失败: %v", err)
		return nil
	}

	if !config.Enabled {
		logger.Infof("Telegram通知服务已禁用")
		return nil
	}

	client := &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
		baseURL: fmt.Sprintf("https://api.telegram.org/bot%s", config.BotToken),
	}

	logger.Infof("Telegram客户端初始化成功")
	return client
}

// SendMessage 发送消息
func (c *Client) SendMessage(text string) error {
	if c == nil {
		return ErrServiceDisabled
	}

	message := &TelegramMessage{
		ChatID:    c.config.ChatID,
		Text:      text,
		ParseMode: "Markdown",
	}

	return c.sendMessageWithRetry(message)
}

// sendMessageWithRetry 带重试的发送消息
func (c *Client) sendMessageWithRetry(message *TelegramMessage) error {
	var lastErr error

	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			// 重试前等待
			waitTime := time.Duration(attempt) * time.Second
			logger.Infof("Telegram发送失败，%v后重试 (第%d次)", waitTime, attempt+1)
			time.Sleep(waitTime)
		}

		err := c.sendMessage(message)
		if err == nil {
			if attempt > 0 {
				logger.Infof("Telegram消息重试发送成功")
			}
			return nil
		}

		lastErr = err
		logger.Warnf("Telegram发送失败 (第%d次): %v", attempt+1, err)
	}

	return fmt.Errorf("发送失败，已重试%d次: %w", c.config.MaxRetries, lastErr)
}

// sendMessage 发送单条消息
func (c *Client) sendMessage(message *TelegramMessage) error {
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %w", err)
	}

	url := fmt.Sprintf("%s/sendMessage", c.baseURL)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	var telegramResp TelegramResponse
	if err := json.Unmarshal(body, &telegramResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	if !telegramResp.OK {
		return fmt.Errorf("Telegram API错误 [%d]: %s", telegramResp.ErrorCode, telegramResp.Description)
	}

	logger.Infof("Telegram消息发送成功 (MessageID: %d)", telegramResp.Result.MessageID)
	return nil
}