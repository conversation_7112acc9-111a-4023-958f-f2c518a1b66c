package telegram

import (
	"time"
)

// Config Telegram配置
type Config struct {
	Enabled   bool          `mapstructure:"enabled"`    // 是否启用Telegram通知
	BotToken  string        `mapstructure:"bot_token"`  // Bot API Token
	ChatID    string        `mapstructure:"chat_id"`    // 目标聊天ID
	Timeout   time.Duration `mapstructure:"timeout"`    // 请求超时时间
	MaxRetries int          `mapstructure:"max_retries"` // 最大重试次数
	
	// 通知配置
	Notifications NotificationConfig `mapstructure:"notifications"`
}

// NotificationConfig 通知配置
type NotificationConfig struct {
	// 采集通知
	ScrapingProgress  bool `mapstructure:"scraping_progress"`  // 采集进度通知
	ScrapingCompleted bool `mapstructure:"scraping_completed"` // 采集完成通知
	ScrapingFailed    bool `mapstructure:"scraping_failed"`    // 采集失败通知
	
	// 上传通知
	UploadProgress   bool `mapstructure:"upload_progress"`   // 上传进度通知
	UploadCompleted  bool `mapstructure:"upload_completed"`  // 上传完成通知
	UploadFailed     bool `mapstructure:"upload_failed"`     // 上传失败通知
	
	// 系统通知
	SystemError bool `mapstructure:"system_error"` // 系统错误通知
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Enabled:    false,
		BotToken:   "",
		ChatID:     "",
		Timeout:    30 * time.Second,
		MaxRetries: 3,
		Notifications: NotificationConfig{
			ScrapingProgress:  true,
			ScrapingCompleted: true,
			ScrapingFailed:    true,
			UploadProgress:    true,
			UploadCompleted:   true,
			UploadFailed:      true,
			SystemError:       true,
		},
	}
}

// Validate 验证配置
func (c *Config) Validate() error {
	if !c.Enabled {
		return nil
	}
	
	if c.BotToken == "" {
		return ErrInvalidBotToken
	}
	
	if c.ChatID == "" {
		return ErrInvalidChatID
	}
	
	if c.Timeout <= 0 {
		c.Timeout = 30 * time.Second
	}
	
	if c.MaxRetries <= 0 {
		c.MaxRetries = 3
	}
	
	return nil
}