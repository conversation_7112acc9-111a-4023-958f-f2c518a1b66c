package telegram

import (
	"fmt"
	"strings"
	"time"
)

// FormatUploadCompleted 格式化上传完成通知
func FormatUploadCompleted(stats *UploadStats) string {
	var msg strings.Builder
	
	// 标题
	msg.WriteString("🚀 *文件上传任务完成*\n")
	msg.WriteString("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")
	
	// 基本统计
	msg.WriteString("📊 *上传统计*\n")
	msg.WriteString(fmt.Sprintf("🔍 总扫描次数: `%d` 次\n", stats.TotalScans))
	msg.WriteString(fmt.Sprintf("📤 总上传文件: `%d` 个\n", stats.TotalUploads))
	msg.WriteString("\n")
	
	// 成功失败统计
	msg.WriteString("✅ *成功统计*\n")
	msg.WriteString(fmt.Sprintf("🎯 成功上传: `%d` 个\n", stats.SuccessfulUploads))
	msg.WriteString(fmt.Sprintf("📊 上传数据: `%s`\n", formatFileSize(stats.TotalDataUploaded)))
	msg.WriteString("\n")
	
	// 失败统计
	if stats.FailedUploads > 0 {
		msg.WriteString("❌ *失败统计*\n")
		msg.WriteString(fmt.Sprintf("💥 失败上传: `%d` 个\n", stats.FailedUploads))
		msg.WriteString("\n")
	}
	
	// 性能统计
	msg.WriteString("⚡ *性能统计*\n")
	msg.WriteString(fmt.Sprintf("🎯 成功率: `%.1f%%`\n", stats.UploadSuccessRate))
	if stats.Duration > 0 {
		msg.WriteString(fmt.Sprintf("⏱️ 总耗时: `%v`\n", formatDuration(stats.Duration)))
		if stats.TotalUploads > 0 {
			avgPerFile := stats.Duration / time.Duration(stats.TotalUploads)
			msg.WriteString(fmt.Sprintf("📁 平均每文件: `%v`\n", formatDuration(avgPerFile)))
		}
	}
	msg.WriteString("\n")
	
	// 时间信息
	if !stats.LastUploadTime.IsZero() {
		msg.WriteString("🕐 *时间信息*\n")
		msg.WriteString(fmt.Sprintf("📤 最后上传: `%s`\n", stats.LastUploadTime.Format("2006-01-02 15:04:05")))
		msg.WriteString("\n")
	}
	
	// 结尾
	msg.WriteString("🎯 *上传完成状态*\n")
	msg.WriteString("☁️ 文件已上传到云端\n")
	msg.WriteString("🗑️ 本地文件已清理\n")
	msg.WriteString("📋 上传记录已保存\n")
	
	return msg.String()
}

// FormatUploadProgress 格式化上传进度通知
func FormatUploadProgress(fileName string, progress float64, provider string) string {
	var msg strings.Builder
	
	msg.WriteString("📤 *文件上传中*\n")
	msg.WriteString("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")
	
	msg.WriteString(fmt.Sprintf("📁 文件名: `%s`\n", fileName))
	msg.WriteString(fmt.Sprintf("☁️ 服务商: `%s`\n", provider))
	msg.WriteString(fmt.Sprintf("📊 进度: `%.1f%%`\n", progress))
	
	// 进度条
	progressBar := generateProgressBar(progress)
	msg.WriteString(fmt.Sprintf("▓ %s\n", progressBar))
	
	return msg.String()
}

// FormatUploadFailed 格式化上传失败通知
func FormatUploadFailed(fileName string, provider string, errorMsg string) string {
	var msg strings.Builder
	
	msg.WriteString("❌ *文件上传失败*\n")
	msg.WriteString("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")
	
	msg.WriteString(fmt.Sprintf("📁 文件名: `%s`\n", fileName))
	msg.WriteString(fmt.Sprintf("☁️ 服务商: `%s`\n", provider))
	msg.WriteString(fmt.Sprintf("💥 错误信息: `%s`\n", errorMsg))
	
	return msg.String()
}