package telegram

import (
	"errors"
	"time"
)

// 错误定义
var (
	ErrInvalidBotToken = errors.New("invalid bot token")
	ErrInvalidChatID   = errors.New("invalid chat ID")
	ErrSendFailed      = errors.New("failed to send message")
	ErrServiceDisabled = errors.New("telegram service is disabled")
)

// NotificationType 通知类型
type NotificationType string

const (
	// 采集通知
	NotificationScrapingProgress  NotificationType = "scraping_progress"
	NotificationScrapingCompleted NotificationType = "scraping_completed"
	NotificationScrapingFailed    NotificationType = "scraping_failed"
	
	// 上传通知
	NotificationUploadProgress   NotificationType = "upload_progress"
	NotificationUploadCompleted  NotificationType = "upload_completed"
	NotificationUploadFailed     NotificationType = "upload_failed"
	
	// 系统通知
	NotificationSystemError NotificationType = "system_error"
)

// ScrapingStats 采集统计数据
type ScrapingStats struct {
	TotalPages         int           `json:"total_pages"`
	TotalMovies        int           `json:"total_movies"`
	ProcessedMovies    int           `json:"processed_movies"`
	SuccessMovies      int           `json:"success_movies"`
	FailedMovies       int           `json:"failed_movies"`
	NewMovies          int           `json:"new_movies"`
	UpdatedMovies      int           `json:"updated_movies"`
	DownloadsCreated   int           `json:"downloads_created"`
	ImagesDownloaded   int           `json:"images_downloaded"`
	StartTime          time.Time     `json:"start_time"`
	EndTime            time.Time     `json:"end_time"`
	Duration           time.Duration `json:"duration"`
	SuccessRate        float64       `json:"success_rate"`
	ErrorCount         int           `json:"error_count"`
}

// UploadStats 上传统计数据
type UploadStats struct {
	TotalScans        int64         `json:"total_scans"`
	TotalUploads      int64         `json:"total_uploads"`
	SuccessfulUploads int64         `json:"successful_uploads"`
	FailedUploads     int64         `json:"failed_uploads"`
	TotalDataUploaded int64         `json:"total_data_uploaded"`
	LastUploadTime    time.Time     `json:"last_upload_time"`
	UploadSuccessRate float64       `json:"upload_success_rate"`
	Duration          time.Duration `json:"duration"`
}

// TelegramMessage Telegram消息结构
type TelegramMessage struct {
	ChatID    string `json:"chat_id"`
	Text      string `json:"text"`
	ParseMode string `json:"parse_mode,omitempty"`
}

// TelegramResponse Telegram API响应
type TelegramResponse struct {
	OK          bool   `json:"ok"`
	Description string `json:"description,omitempty"`
	ErrorCode   int    `json:"error_code,omitempty"`
	Result      struct {
		MessageID int `json:"message_id"`
	} `json:"result,omitempty"`
}