package telegram

import (
	"fmt"
	"strings"
	"time"
)

// FormatScrapingCompleted 格式化采集完成通知
func FormatScrapingCompleted(stats *ScrapingStats) string {
	var msg strings.Builder
	
	// 标题
	msg.WriteString("🎉 *JAV采集任务完成*\n")
	msg.WriteString("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n")
	
	// 基本统计
	msg.WriteString("📊 *采集统计*\n")
	msg.WriteString(fmt.Sprintf("📄 总页数: `%d` 页\n", stats.TotalPages))
	msg.WriteString(fmt.Sprintf("🎬 发现影片: `%d` 部\n", stats.TotalMovies))
	msg.WriteString(fmt.Sprintf("⚙️ 处理影片: `%d` 部\n", stats.ProcessedMovies))
	msg.WriteString("\n")
	
	// 成功失败统计
	msg.WriteString("✅ *成功统计*\n")
	msg.WriteString(fmt.Sprintf("🎯 成功采集: `%d` 部\n", stats.SuccessMovies))
	msg.WriteString(fmt.Sprintf("🆕 新增影片: `%d` 部\n", stats.NewMovies))
	msg.WriteString(fmt.Sprintf("🔄 更新影片: `%d` 部\n", stats.UpdatedMovies))
	msg.WriteString(fmt.Sprintf("📥 下载任务: `%d` 个\n", stats.DownloadsCreated))
	msg.WriteString(fmt.Sprintf("🖼️ 下载图片: `%d` 张\n", stats.ImagesDownloaded))
	msg.WriteString("\n")
	
	// 失败统计
	if stats.FailedMovies > 0 {
		msg.WriteString("❌ *失败统计*\n")
		msg.WriteString(fmt.Sprintf("💥 失败采集: `%d` 部\n", stats.FailedMovies))
		msg.WriteString(fmt.Sprintf("⚠️ 错误数量: `%d` 个\n", stats.ErrorCount))
		msg.WriteString("\n")
	}
	
	// 性能统计
	msg.WriteString("⚡ *性能统计*\n")
	msg.WriteString(fmt.Sprintf("🎯 成功率: `%.1f%%`\n", stats.SuccessRate))
	msg.WriteString(fmt.Sprintf("⏱️ 总耗时: `%v`\n", formatDuration(stats.Duration)))
	if stats.TotalPages > 0 {
		avgPerPage := stats.Duration / time.Duration(stats.TotalPages)
		msg.WriteString(fmt.Sprintf("📄 平均每页: `%v`\n", formatDuration(avgPerPage)))
	}
	if stats.ProcessedMovies > 0 {
		avgPerMovie := stats.Duration / time.Duration(stats.ProcessedMovies)
		msg.WriteString(fmt.Sprintf("🎬 平均每部: `%v`\n", formatDuration(avgPerMovie)))
	}
	msg.WriteString("\n")
	
	// 时间信息
	msg.WriteString("🕐 *时间信息*\n")
	msg.WriteString(fmt.Sprintf("🚀 开始时间: `%s`\n", stats.StartTime.Format("2006-01-02 15:04:05")))
	msg.WriteString(fmt.Sprintf("🏁 结束时间: `%s`\n", stats.EndTime.Format("2006-01-02 15:04:05")))
	msg.WriteString("\n")
	
	// 结尾
	msg.WriteString("🎯 *任务完成状态*\n")
	msg.WriteString("💾 数据已保存到数据库\n")
	msg.WriteString("🖼️ 图片已下载到本地\n")
	msg.WriteString("📥 下载任务已创建，aria2将自动开始下载\n")
	
	return msg.String()
}