package streamhg

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"magnet-downloader/pkg/logger"
)

// UploadResult 上传结果
type UploadResult struct {
	Success  bool   `json:"success"`
	URL      string `json:"url"`       // 文件页面URL
	PlayURL  string `json:"play_url"`  // 播放URL
	FileCode string `json:"file_code"` // 文件代码
	Size     int64  `json:"size"`      // 文件大小
	Title    string `json:"title"`     // 文件标题
	CanPlay  bool   `json:"can_play"`  // 是否可播放
	Error    string `json:"error"`     // 错误信息
}

// UploadFile 上传文件到StreamHG
func (c *Client) UploadFile(filePath string) (*UploadResult, error) {
	// 验证文件路径
	if filePath == "" {
		return nil, NewValidationError("文件路径不能为空", "filePath is empty")
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(filePath)
	if os.IsNotExist(err) {
		return nil, NewFileError(ErrorTypeFileAccess, "文件不存在", err)
	}
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "无法访问文件", err)
	}

	// 检查文件大小
	if fileInfo.Size() == 0 {
		return nil, NewFileError(ErrorTypeFileSize, "文件大小为0", fmt.Errorf("file size is 0"))
	}

	// StreamHG支持最大15GB文件
	maxSize := int64(15 * 1024 * 1024 * 1024) // 15GB
	if fileInfo.Size() > maxSize {
		return nil, NewFileError(ErrorTypeFileSize,
			fmt.Sprintf("文件过大，StreamHG最大支持15GB，当前文件%.2fGB",
				float64(fileInfo.Size())/(1024*1024*1024)),
			fmt.Errorf("file too large"))
	}

	originalFilename := filepath.Base(filePath)
	// 应用智能文件重命名
	filename := smartRenameVideoFile(originalFilename)
	logger.Infof("开始智能上传文件到StreamHG: %s (原名: %s, size: %d bytes)", filename, originalFilename, fileInfo.Size())

	// 智能上传策略：
	// 1. 小文件(<50MB): 缓冲上传（最佳兼容性）
	// 2. 大文件(≥50MB): 零内存流式上传（节省内存）
	bufferThreshold := int64(50 * 1024 * 1024) // 50MB

	if fileInfo.Size() < bufferThreshold {
		logger.Infof("小文件(%.2f MB)，使用缓冲上传（确保兼容性）", float64(fileInfo.Size())/(1024*1024))
		return c.performBufferedUpload(filePath, filename, "")
	} else {
		logger.Infof("大文件(%.2f MB)，使用零内存流式上传（节省内存）", float64(fileInfo.Size())/(1024*1024))
		return c.performZeroMemoryUpload(filePath, filename, "")
	}
} // performBufferedUpload 执行缓冲上传（兼容性更好）
func (c *Client) performBufferedUpload(filePath, filename, folderID string) (*UploadResult, error) {
	var lastErr error

	// 重试上传
	for attempt := 0; attempt < c.config.MaxRetries; attempt++ {
		if attempt > 0 {
			waitTime := c.calculateBackoff(attempt, IsRateLimitError(lastErr))
			logger.Debugf("重试StreamHG缓冲上传，等待 %v (attempt %d/%d)", waitTime, attempt+1, c.config.MaxRetries)
			time.Sleep(waitTime)
		}

		// 1. 获取上传服务器URL
		serverURL, err := c.GetUploadServer(folderID)
		if err != nil {
			lastErr = err
			logger.Warnf("获取StreamHG上传服务器失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
			continue
		}

		// 2. 执行缓冲文件上传
		result, err := c.performBufferedFileUpload(serverURL, filePath, filename)
		if err == nil {
			logger.Infof("StreamHG缓冲上传成功: %s", filename)
			return result, nil
		}

		lastErr = err
		logger.Warnf("StreamHG缓冲上传失败 (attempt %d/%d): %v", attempt+1, c.config.MaxRetries, err)
	}

	return nil, NewUploadError("StreamHG上传失败",
		fmt.Sprintf("所有重试都失败了，最后错误: %v", lastErr), 500)
}

// performBufferedFileUpload 执行缓冲文件上传
func (c *Client) performBufferedFileUpload(serverURL, filePath, filename string) (*UploadResult, error) {
	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "打开文件失败", err)
	}
	defer file.Close()

	// 获取文件信息
	fileInfo, err := file.Stat()
	if err != nil {
		return nil, NewFileError(ErrorTypeFileAccess, "获取文件信息失败", err)
	}

	// 应用速率限制
	c.applyRateLimit()

	logger.Infof("缓冲上传文件到StreamHG: %s (size: %.2f MB) -> %s",
		filename, float64(fileInfo.Size())/(1024*1024), serverURL)

	// 使用传统的内存缓冲上传
	var buf bytes.Buffer
	writer := multipart.NewWriter(&buf)

	// 创建文件字段 (StreamHG使用"file"字段名)
	part, err := writer.CreateFormFile("file", filename)
	if err != nil {
		return nil, NewUploadError("创建文件字段失败", err.Error(), 500)
	}

	// 复制文件内容到buffer
	_, err = io.Copy(part, file)
	if err != nil {
		return nil, NewUploadError("复制文件内容失败", err.Error(), 500)
	}

	// 添加API key字段
	writer.WriteField("key", c.config.APIKey)

	// 关闭writer
	writer.Close()

	logger.Infof("缓冲上传数据准备完成: %d bytes", buf.Len())

	// 创建请求
	req, err := http.NewRequest("POST", serverURL, &buf)
	if err != nil {
		return nil, NewUploadError("创建上传请求失败", err.Error(), 500)
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	req.Header.Set("User-Agent", "magnet-downloader/1.0")

	// 执行请求
	logger.Infof("执行StreamHG缓冲上传请求到: %s", serverURL)
	resp, err := c.httpClient.Do(req)
	if err != nil {
		logger.Errorf("StreamHG HTTP请求失败: %v", err)
		return nil, NewNetworkError("StreamHG HTTP请求失败", fmt.Sprintf("请求URL: %s, 错误: %v", serverURL, err))
	}
	defer resp.Body.Close()

	// 处理响应
	return c.handleUploadResponse(resp, filename)
} // handleUploadResponse 处理上传响应
func (c *Client) handleUploadResponse(resp *http.Response, filename string) (*UploadResult, error) {
	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewUploadError("读取响应失败", err.Error(), resp.StatusCode)
	}

	logger.Infof("StreamHG上传响应 (HTTP %d): %s", resp.StatusCode, string(body))

	// 解析JSON响应
	return c.parseStreamHGResponse(body, resp.StatusCode, filename)
}

// parseStreamHGResponse 解析StreamHG响应
func (c *Client) parseStreamHGResponse(body []byte, statusCode int, filename string) (*UploadResult, error) {
	var response map[string]interface{}
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, NewUploadError("解析响应JSON失败", err.Error(), statusCode)
	}

	// 检查响应状态
	status, ok := response["status"].(float64)
	if !ok {
		return nil, NewUploadError("无效的响应格式", "缺少status字段", statusCode)
	}

	if status != 200 {
		msg := "Unknown error"
		if msgStr, exists := response["msg"].(string); exists {
			msg = msgStr
		}
		return &UploadResult{
			Success: false,
			Error:   msg,
		}, NewUploadError("StreamHG上传失败", msg, int(status))
	}

	// 解析文件信息
	files, ok := response["files"].([]interface{})
	if !ok || len(files) == 0 {
		return nil, NewUploadError("无效的响应格式", "缺少files字段或为空", statusCode)
	}

	// 获取第一个文件的信息
	fileData, ok := files[0].(map[string]interface{})
	if !ok {
		return nil, NewUploadError("无效的文件数据格式", "files[0]不是对象", statusCode)
	}

	// 提取文件代码
	fileCode, _ := fileData["filecode"].(string)
	if fileCode == "" {
		return nil, NewUploadError("无效的响应", "缺少filecode", statusCode)
	}

	// 构建播放URL
	playURL := fmt.Sprintf("https://streamhg.com/v/%s", fileCode)
	pageURL := fmt.Sprintf("https://streamhg.com/%s.html", fileCode)

	result := &UploadResult{
		Success:  true,
		URL:      pageURL,
		PlayURL:  playURL,
		FileCode: fileCode,
		Title:    filename,
		CanPlay:  true,
		Error:    "",
	}

	// 尝试获取文件大小
	if sizeStr, ok := fileData["size"].(string); ok {
		// 这里可以解析大小字符串，暂时跳过
		_ = sizeStr
	}

	logger.Infof("StreamHG上传成功: %s -> %s", filename, result.PlayURL)
	return result, nil
}

// calculateBackoff 计算退避时间
func (c *Client) calculateBackoff(attempt int, isRateLimit bool) time.Duration {
	if isRateLimit {
		// 速率限制错误使用更长的退避时间
		return time.Duration(attempt*attempt*5) * time.Second
	}

	// 普通错误使用指数退避
	backoff := time.Duration(attempt*attempt) * time.Second
	if backoff > 30*time.Second {
		backoff = 30 * time.Second
	}
	return backoff
}

// smartRenameVideoFile 智能重命名视频文件
// 将所有视频文件重命名为 getav.net@xxx.mp4/mkv 格式
func smartRenameVideoFile(originalFilename string) string {
	// 如果文件名已经以 getav.net@ 开头，保持原样
	if strings.HasPrefix(originalFilename, "getav.net@") {
		logger.Debugf("文件名已经是getav.net格式，保持原样: %s", originalFilename)
		return originalFilename
	}

	var baseFilename string

	// 检查文件名是否包含@符号
	if strings.Contains(originalFilename, "@") {
		// 分割文件名，取@后面的部分
		parts := strings.SplitN(originalFilename, "@", 2)
		if len(parts) == 2 {
			domainPart := parts[0]
			filenamePart := parts[1]

			// 检查域名部分是否包含域名格式
			domainPattern := regexp.MustCompile(`^[a-zA-Z0-9\-]+\.(com|net|org|cn|jp|tv|xxx|info|biz)$`)
			if domainPattern.MatchString(domainPart) {
				// 是域名格式，使用@后面的部分
				baseFilename = filenamePart
				logger.Debugf("检测到域名格式，提取文件名部分: %s", baseFilename)
			} else {
				// 不是域名格式，使用整个文件名
				baseFilename = originalFilename
			}
		} else {
			// 分割失败，使用整个文件名
			baseFilename = originalFilename
		}
	} else {
		// 没有@符号，直接使用原文件名
		baseFilename = originalFilename
	}

	// StreamHG只支持MP4格式，需要将其他视频格式的扩展名转换为.mp4
	ext := filepath.Ext(baseFilename)
	nameWithoutExt := strings.TrimSuffix(baseFilename, ext)
	
	// 检查是否为视频格式，如果是则统一使用.mp4扩展名
	videoExtensions := []string{".mkv", ".avi", ".mov", ".wmv", ".flv", ".webm", ".m4v", ".MKV", ".AVI", ".MOV", ".WMV"}
	isVideoFormat := false
	for _, videoExt := range videoExtensions {
		if strings.ToLower(ext) == strings.ToLower(videoExt) {
			isVideoFormat = true
			break
		}
	}
	
	// 如果是视频格式但不是mp4，则转换为mp4扩展名
	if isVideoFormat {
		ext = ".mp4"
	} else if ext == "" {
		ext = ".mp4" // 默认扩展名
	}
	
	// 生成新的文件名
	newFilename := fmt.Sprintf("getav.net@%s%s", nameWithoutExt, ext)

	logger.Infof("智能重命名视频文件: %s -> %s (StreamHG兼容格式)", originalFilename, newFilename)
	return newFilename
}
