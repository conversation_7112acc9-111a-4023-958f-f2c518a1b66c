#!/usr/bin/env python3
"""
115网盘登录工具
"""

import requests
import json
import time
import sys
import os

class Pan115Login:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.cookies_file = '/www/wwwroot/JAVAPI.COM/115_cookies.json'
    
    def get_qr_code(self):
        """获取登录二维码"""
        try:
            qr_url = "https://qrcodeapi.115.com/api/1.0/web/1.0/token/"
            response = self.session.get(qr_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('state'):
                    return data['data']
            
        except Exception as e:
            print(f"获取二维码失败: {e}")
        
        return None
    
    def wait_for_scan(self, uid):
        """等待扫码登录"""
        check_url = f"https://qrcodeapi.115.com/get/status/?uid={uid}"
        
        print("\n⏳ 等待扫码登录...")
        print("📱 请在2分钟内使用115手机APP扫描二维码")
        
        for i in range(120):  # 等待2分钟
            try:
                response = self.session.get(check_url, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get('data', {}).get('status', 0)
                    
                    if status == 2:
                        # 扫码成功，获取登录信息
                        print("\n✅ 扫码成功！正在登录...")
                        return self.complete_login(uid)
                    
                    elif status == -1:
                        print("\n❌ 二维码已过期，请重新获取")
                        return False
                
            except Exception as e:
                if i % 10 == 0:
                    print(f"检查状态时出错: {e}")
            
            # 每5秒显示一次倒计时
            if i % 5 == 0 and i > 0:
                remaining = 120 - i
                print(f"⏰ 剩余时间: {remaining}秒")
            
            time.sleep(1)
        
        print("\n⏰ 等待超时，请重新获取二维码")
        return False
    
    def complete_login(self, uid):
        """完成登录流程"""
        try:
            # 获取登录cookie
            login_url = f"https://passportapi.115.com/app/1.0/web/1.0/login/qrcode/?account={uid}"
            response = self.session.post(login_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('state'):
                    # 保存cookies
                    self.save_cookies()
                    
                    # 获取用户信息
                    user_info = self.get_user_info()
                    if user_info:
                        print(f"🎉 登录成功！")
                        print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
                        print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
                        return True
            
        except Exception as e:
            print(f"完成登录时出错: {e}")
        
        return False
    
    def save_cookies(self):
        """保存登录cookies"""
        try:
            cookies_dict = dict(self.session.cookies)
            with open(self.cookies_file, 'w') as f:
                json.dump(cookies_dict, f, indent=2)
            print(f"💾 登录信息已保存到: {self.cookies_file}")
        except Exception as e:
            print(f"保存登录信息失败: {e}")
    
    def load_cookies(self):
        """加载保存的cookies"""
        if os.path.exists(self.cookies_file):
            try:
                with open(self.cookies_file, 'r') as f:
                    cookies = json.load(f)
                    self.session.cookies.update(cookies)
                return True
            except Exception as e:
                print(f"加载登录信息失败: {e}")
        return False
    
    def get_user_info(self):
        """获取用户信息"""
        try:
            url = "https://my.115.com/?ct=guide&ac=status"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('state'):
                    return data.get('data', {})
        except Exception as e:
            print(f"获取用户信息失败: {e}")
        
        return None
    
    def check_login_status(self):
        """检查登录状态"""
        if self.load_cookies():
            user_info = self.get_user_info()
            if user_info:
                print("✅ 已登录")
                print(f"👤 用户名: {user_info.get('user_name', 'Unknown')}")
                print(f"📧 邮箱: {user_info.get('email', 'Unknown')}")
                return True
        
        print("❌ 未登录或登录已过期")
        return False
    
    def login(self):
        """执行登录流程"""
        print("🔐 115网盘登录")
        print("=" * 40)
        
        # 获取二维码
        qr_data = self.get_qr_code()
        if not qr_data:
            print("❌ 获取二维码失败")
            return False
        
        uid = qr_data['uid']
        qr_url = qr_data['qrcode']
        
        print(f"📱 请使用115手机APP扫描以下二维码:")
        print(f"🔗 {qr_url}")
        print(f"🆔 UID: {uid}")
        
        # 等待扫码
        return self.wait_for_scan(uid)

def main():
    login_tool = Pan115Login()
    
    if len(sys.argv) < 2:
        print("115网盘登录工具")
        print("用法:")
        print("  python3 115_login.py login   # 二维码登录")
        print("  python3 115_login.py status  # 检查登录状态")
        return
    
    command = sys.argv[1]
    
    if command == 'login':
        login_tool.login()
    elif command == 'status':
        login_tool.check_login_status()
    else:
        print(f"未知命令: {command}")

if __name__ == '__main__':
    main()