#!/usr/bin/env python3
"""
高级磁力链接转直链工具
集成多个API服务和本地处理
"""

import requests
import time
import subprocess
import re
import json
import sys
import os
from urllib.parse import quote, unquote
import threading
from datetime import datetime

class AdvancedMagnetConverter:
    def __init__(self):
        self.services = {
            'webtor': {
                'name': 'Webtor.io',
                'base_url': 'https://webtor.io',
                'api_url': 'https://api.webtor.io',
                'enabled': True
            },
            'instant': {
                'name': 'Instant.io',
                'base_url': 'https://instant.io',
                'enabled': True
            },
            'btcache': {
                'name': 'BT Cache',
                'base_url': 'https://btcache.me',
                'enabled': True
            }
        }
        
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })
        
        self.last_clipboard = ""
        self.results_cache = {}
        
    def log(self, message, level="INFO"):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        icons = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "ERROR": "❌",
            "WARNING": "⚠️",
            "PROCESS": "🔄"
        }
        icon = icons.get(level, "📝")
        print(f"[{timestamp}] {icon} {message}")
    
    def get_clipboard_content(self):
        """获取剪贴板内容"""
        try:
            env = {'DISPLAY': ':1'}
            result = subprocess.run(['xclip', '-selection', 'clipboard', '-o'], 
                                  capture_output=True, text=True, env=env)
            return result.stdout.strip()
        except Exception as e:
            self.log(f"获取剪贴板内容失败: {e}", "ERROR")
            return ""
    
    def is_magnet_link(self, text):
        """检查是否为磁力链接"""
        return bool(re.match(r'^magnet:\?xt=urn:btih:[a-fA-F0-9]{40}', text))
    
    def extract_magnet_info(self, magnet_url):
        """提取磁力链接信息"""
        info = {}
        
        # 提取hash
        hash_match = re.search(r'xt=urn:btih:([a-fA-F0-9]{40})', magnet_url)
        if hash_match:
            info['hash'] = hash_match.group(1)
        
        # 提取显示名称
        dn_match = re.search(r'dn=([^&]+)', magnet_url)
        if dn_match:
            info['name'] = unquote(dn_match.group(1))
        
        # 提取tracker
        tr_matches = re.findall(r'tr=([^&]+)', magnet_url)
        if tr_matches:
            info['trackers'] = [unquote(tr) for tr in tr_matches]
        
        return info
    
    def try_webtor_api(self, magnet_url, magnet_info):
        """尝试Webtor.io API"""
        try:
            self.log("尝试 Webtor.io API...", "PROCESS")
            
            # 方法1: 直接构造webtor.io URL
            encoded_magnet = quote(magnet_url)
            webtor_url = f"https://webtor.io/?magnet={encoded_magnet}"
            
            # 方法2: 尝试API调用
            api_data = {
                'magnet': magnet_url,
                'hash': magnet_info.get('hash', '')
            }
            
            # 检查webtor.io是否可访问
            response = self.session.head("https://webtor.io", timeout=10)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'service': 'Webtor.io',
                    'web_url': webtor_url,
                    'type': 'web_interface',
                    'description': '在线播放和下载'
                }
            else:
                return {'success': False, 'error': 'Webtor.io 不可访问'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def try_instant_io(self, magnet_url, magnet_info):
        """尝试Instant.io"""
        try:
            self.log("尝试 Instant.io...", "PROCESS")
            
            instant_url = f"https://instant.io/#{magnet_url}"
            
            # 检查服务可用性
            response = self.session.head("https://instant.io", timeout=10)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'service': 'Instant.io',
                    'web_url': instant_url,
                    'type': 'web_interface',
                    'description': '即时下载和流媒体'
                }
            else:
                return {'success': False, 'error': 'Instant.io 不可访问'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def try_btcache_me(self, magnet_url, magnet_info):
        """尝试BTCache.me"""
        try:
            self.log("尝试 BTCache.me...", "PROCESS")
            
            hash_value = magnet_info.get('hash')
            if not hash_value:
                return {'success': False, 'error': '无法提取hash值'}
            
            btcache_url = f"https://btcache.me/torrent/{hash_value}"
            
            # 检查服务可用性
            response = self.session.head("https://btcache.me", timeout=10)
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'service': 'BTCache.me',
                    'web_url': btcache_url,
                    'type': 'web_interface',
                    'description': 'Torrent缓存和下载'
                }
            else:
                return {'success': False, 'error': 'BTCache.me 不可访问'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_torrent_metadata(self, magnet_url):
        """获取torrent元数据"""
        try:
            self.log("尝试获取torrent元数据...", "PROCESS")
            
            # 使用aria2c获取torrent信息
            cmd = [
                'aria2c', 
                '--bt-metadata-only=true',
                '--bt-save-metadata=true',
                '--quiet=true',
                '--dir=/tmp',
                magnet_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log("成功获取torrent元数据", "SUCCESS")
                return {'success': True, 'metadata': 'available'}
            else:
                return {'success': False, 'error': 'aria2c获取元数据失败'}
                
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': '获取元数据超时'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def process_magnet_link(self, magnet_url):
        """处理磁力链接"""
        self.log("=" * 60)
        self.log("检测到磁力链接!", "SUCCESS")
        self.log("=" * 60)
        
        # 提取磁力链接信息
        magnet_info = self.extract_magnet_info(magnet_url)
        
        print(f"📋 磁力链接: {magnet_url[:80]}...")
        if magnet_info.get('hash'):
            print(f"🔑 Hash: {magnet_info['hash']}")
        if magnet_info.get('name'):
            print(f"📁 名称: {magnet_info['name']}")
        
        # 尝试各种服务
        services_to_try = [
            ('webtor', self.try_webtor_api),
            ('instant', self.try_instant_io),
            ('btcache', self.try_btcache_me)
        ]
        
        successful_results = []
        
        for service_name, service_func in services_to_try:
            if self.services[service_name]['enabled']:
                result = service_func(magnet_url, magnet_info)
                if result.get('success'):
                    successful_results.append(result)
                    self.log(f"{result['service']} 成功", "SUCCESS")
                else:
                    self.log(f"{self.services[service_name]['name']} 失败: {result.get('error', '未知错误')}", "WARNING")
        
        # 显示结果
        if successful_results:
            print("\n🎯 可用的在线服务:")
            print("-" * 50)
            for i, result in enumerate(successful_results, 1):
                print(f"{i}. {result['service']}")
                print(f"   🌐 链接: {result['web_url']}")
                print(f"   📝 说明: {result['description']}")
                print()
        else:
            self.log("所有服务都不可用", "ERROR")
        
        # 尝试获取元数据
        metadata_result = self.get_torrent_metadata(magnet_url)
        if metadata_result.get('success'):
            self.log("已获取torrent元数据，可以进行本地处理", "SUCCESS")
        
        # 提供使用指导
        print("📖 使用指导:")
        print("-" * 30)
        print("1. 点击上面的链接在浏览器中打开")
        print("2. 等待torrent解析完成")
        print("3. 选择要下载的文件")
        print("4. 获取直接下载链接或在线播放")
        print("5. 某些服务支持流媒体播放")
        
        return successful_results
    
    def monitor_clipboard(self):
        """监听剪贴板变化"""
        self.log("开始监听剪贴板中的磁力链接...", "INFO")
        self.log("复制磁力链接到剪贴板即可自动处理", "INFO")
        self.log("按 Ctrl+C 退出", "INFO")
        print("-" * 50)
        
        while True:
            try:
                current_clipboard = self.get_clipboard_content()
                
                if current_clipboard and current_clipboard != self.last_clipboard:
                    if self.is_magnet_link(current_clipboard):
                        self.process_magnet_link(current_clipboard)
                        print("\n" + "="*50)
                        self.log("等待下一个磁力链接...", "INFO")
                    
                    self.last_clipboard = current_clipboard
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                self.log("程序已退出", "INFO")
                break
            except Exception as e:
                self.log(f"监听过程中出错: {e}", "ERROR")
                time.sleep(5)

def main():
    print("🚀 高级磁力链接转直链工具")
    print("集成多个在线服务和本地处理")
    print("=" * 50)
    
    # 检查依赖
    dependencies = ['xclip', 'aria2c']
    for dep in dependencies:
        try:
            subprocess.run(['which', dep], check=True, capture_output=True)
        except subprocess.CalledProcessError:
            print(f"❌ 未找到 {dep}，正在安装...")
            if dep == 'xclip':
                subprocess.run(['apt', 'install', '-y', 'xclip'], check=True)
            elif dep == 'aria2c':
                subprocess.run(['apt', 'install', '-y', 'aria2'], check=True)
    
    converter = AdvancedMagnetConverter()
    
    if len(sys.argv) > 1:
        # 直接处理命令行参数中的磁力链接
        magnet_url = sys.argv[1]
        if not converter.is_magnet_link(magnet_url):
            print("❌ 请提供有效的磁力链接")
            sys.exit(1)
        
        converter.process_magnet_link(magnet_url)
    else:
        # 监听剪贴板模式
        converter.monitor_clipboard()

if __name__ == "__main__":
    main()