#!/usr/bin/env python3
import requests
import sys

def test_115_api():
    """测试115 API连接"""
    try:
        print("正在测试115 API连接...")
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
        })
        
        # 测试基本连接
        response = session.get('https://115.com/', timeout=10)
        print(f"115.com 连接状态: {response.status_code}")
        
        # 测试API端点
        qr_url = "https://qrcodeapi.115.com/api/1.0/web/1.0/token/"
        response = session.get(qr_url, timeout=10)
        print(f"二维码API状态: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("API响应:", data)
            
            if data.get('state'):
                qr_code = data['data']['qrcode']
                uid = data['data']['uid']
                
                print(f"\n✅ 115登录二维码获取成功!")
                print(f"🔗 二维码链接: {qr_code}")
                print(f"📱 请使用115手机APP扫描上述链接中的二维码")
                print(f"⏰ 二维码有效期: 2分钟")
                
                return uid
        else:
            print("❌ 获取二维码失败")
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
    
    return None

if __name__ == '__main__':
    test_115_api()