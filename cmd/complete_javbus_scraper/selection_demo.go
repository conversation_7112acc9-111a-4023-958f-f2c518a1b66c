package main

import (
	"fmt"
	"strings"
	"magnet-downloader/pkg/javscraper"
)

// demonstrateImprovedSelection 演示改进后的选择逻辑
func demonstrateImprovedSelection() {
	fmt.Println("🎯 演示改进后的磁力链接选择逻辑")
	fmt.Println("=" + strings.Repeat("=", 50))
	
	// 模拟VEC-708的磁力链接情况
	vec708Magnets := []javscraper.MagnetInfo{
		{
			MagnetURL: "magnet:?xt=urn:btih:1111111111111111111111111111111111111111",
			FileName:  "[中文字幕] VEC-708 4K 超高清版本.mp4",
			FileSize:  4000000000, // 4GB - 最大文件，有中文字幕
		},
		{
			MagnetURL: "magnet:?xt=urn:btih:2222222222222222222222222222222222222222",
			FileName:  "VEC-708 4K UHD 无码流出版.mp4",
			FileSize:  3800000000, // 3.8GB - 4K无字幕版本
		},
		{
			MagnetURL: "magnet:?xt=urn:btih:3333333333333333333333333333333333333333",
			FileName:  "VEC-708 1080p HD 高清版本.mp4",
			FileSize:  2000000000, // 2GB - 1080p无字幕版本
		},
		{
			MagnetURL: "magnet:?xt=urn:btih:4444444444444444444444444444444444444444",
			FileName:  "[英文字幕] VEC-708 1080p 英字版.mp4",
			FileSize:  2100000000, // 2.1GB - 1080p英文字幕版本
		},
		{
			MagnetURL: "magnet:?xt=urn:btih:5555555555555555555555555555555555555555",
			FileName:  "VEC-708 720p 标清版本.mp4",
			FileSize:  1000000000, // 1GB - 720p版本
		},
	}

	fmt.Printf("📊 VEC-708 可用磁力链接 (%d个):\n", len(vec708Magnets))
	for i, magnet := range vec708Magnets {
		fmt.Printf("  %d. %s (%.1fGB)\n", i+1, magnet.FileName, float64(magnet.FileSize)/1000000000)
	}
	fmt.Println()

	// 使用改进后的选择逻辑
	fmt.Println("🔍 使用改进后的选择逻辑:")
	selectedMagnets := selectBestMagnetsAdvanced(vec708Magnets, "VEC-708")

	fmt.Println()
	fmt.Printf("✅ 最终选择结果 (%d个):\n", len(selectedMagnets))
	for i, magnet := range selectedMagnets {
		hasSubtitle := "无字幕"
		if magnet.HasSubtitle {
			hasSubtitle = fmt.Sprintf("%s字幕", magnet.SubtitleLanguage)
		}
		fmt.Printf("  %d. %s\n", i+1, magnet.FileName)
		fmt.Printf("     质量: %s | %s | 评分: %.1f | 大小: %.1fGB\n", 
			magnet.Quality, hasSubtitle, magnet.Score, float64(magnet.FileSize)/1000000000)
	}

	fmt.Println()
	fmt.Println("🎯 改进效果:")
	fmt.Println("  ✅ 选择了中文字幕的4K版本（优先级最高）")
	fmt.Println("  ✅ 同时选择了无字幕的4K版本（高质量，不重复）")
	fmt.Println("  ✅ 可能还选择了1080p版本（不同质量级别）")
	fmt.Println("  ❌ 跳过了相似或低质量的版本")
}

// demonstrateOldVsNewLogic 对比旧逻辑和新逻辑
func demonstrateOldVsNewLogic() {
	fmt.Println("\n🔄 旧逻辑 vs 新逻辑对比")
	fmt.Println("=" + strings.Repeat("=", 50))
	
	fmt.Println("📋 旧逻辑问题:")
	fmt.Println("  ❌ 只选择'最大的'无字幕版本")
	fmt.Println("  ❌ 如果中文字幕版本是最大的，就不选其他版本")
	fmt.Println("  ❌ 错过了高质量的无字幕版本")
	
	fmt.Println("\n🎯 新逻辑优势:")
	fmt.Println("  ✅ 质量优先：4K > 1080p > 720p")
	fmt.Println("  ✅ 每个质量级别选择最佳版本")
	fmt.Println("  ✅ 智能去重：避免相似版本")
	fmt.Println("  ✅ 最多选择2个高质量无字幕版本")
	
	fmt.Println("\n📊 VEC-708 选择结果对比:")
	fmt.Println("旧逻辑: 只选择4K中文字幕版本（4GB）")
	fmt.Println("新逻辑: 4K中文字幕版本（4GB）+ 4K无字幕版本（3.8GB）+ 可能的1080p版本（2GB）")
}

// 主演示函数
func runSelectionDemo() {
	demonstrateImprovedSelection()
	demonstrateOldVsNewLogic()
	
	fmt.Println("\n🎉 演示完成！")
	fmt.Println("现在scraper会为每部影片选择更多高质量版本，")
	fmt.Println("确保既有中文字幕版本，也有高清无字幕版本。")
}
