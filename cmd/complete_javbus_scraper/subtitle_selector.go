package main

import (
	"fmt"
	"sort"
)

// selectBestSubtitleMagnets 智能选择字幕磁力链接
// 策略：
// 1. 如果有多个中文字幕，选择最佳的一个（避免重复）
// 2. 如果中文字幕质量差异很大，可以选择2个（如4K+1080p）
// 3. 优先选择中文字幕，其次英文字幕
func selectBestSubtitleMagnets(subtitleMagnets []*ScoredMagnet) []*ScoredMagnet {
	if len(subtitleMagnets) == 0 {
		return nil
	}

	fmt.Printf("        🔍 智能分析 %d 个字幕版本...\n", len(subtitleMagnets))

	// 按语言分组
	chineseMagnets := []*ScoredMagnet{}
	englishMagnets := []*ScoredMagnet{}
	otherMagnets := []*ScoredMagnet{}

	for _, magnet := range subtitleMagnets {
		switch magnet.SubtitleLanguage {
		case "chinese":
			chineseMagnets = append(chineseMagnets, magnet)
		case "english":
			englishMagnets = append(englishMagnets, magnet)
		default:
			otherMagnets = append(otherMagnets, magnet)
		}
	}

	fmt.Printf("        📊 字幕分布: 中文%d个, 英文%d个, 其他%d个\n", 
		len(chineseMagnets), len(englishMagnets), len(otherMagnets))

	var selectedMagnets []*ScoredMagnet

	// 优先处理中文字幕
	if len(chineseMagnets) > 0 {
		selected := selectFromSameLanguage(chineseMagnets, "中文")
		selectedMagnets = append(selectedMagnets, selected...)
	}

	// 如果没有中文字幕，选择英文字幕
	if len(selectedMagnets) == 0 && len(englishMagnets) > 0 {
		selected := selectFromSameLanguage(englishMagnets, "英文")
		selectedMagnets = append(selectedMagnets, selected...)
	}

	// 如果都没有，选择其他语言字幕
	if len(selectedMagnets) == 0 && len(otherMagnets) > 0 {
		selected := selectFromSameLanguage(otherMagnets, "其他")
		selectedMagnets = append(selectedMagnets, selected...)
	}

	return selectedMagnets
}

// selectFromSameLanguage 从同一语言的字幕中选择最佳的
func selectFromSameLanguage(magnets []*ScoredMagnet, language string) []*ScoredMagnet {
	if len(magnets) == 0 {
		return nil
	}

	if len(magnets) == 1 {
		fmt.Printf("        ✅ %s字幕只有1个，直接选择\n", language)
		return magnets
	}

	// 按评分排序
	sort.Slice(magnets, func(i, j int) bool {
		return magnets[i].Score > magnets[j].Score
	})

	fmt.Printf("        🔍 %s字幕有%d个，分析质量差异...\n", language, len(magnets))

	// 分析质量分布
	qualityGroups := groupByQuality(magnets)
	
	// 选择策略
	var selected []*ScoredMagnet

	// 如果有4K和1080p两种高质量版本，都选择
	if hasQuality(qualityGroups, "4K") && hasQuality(qualityGroups, "1080p") {
		if best4K := getBestFromQuality(qualityGroups, "4K"); best4K != nil {
			selected = append(selected, best4K)
			fmt.Printf("        ✅ 选择4K版本: %s\n", best4K.FileName)
		}
		if best1080p := getBestFromQuality(qualityGroups, "1080p"); best1080p != nil {
			// 检查是否与4K版本太相似
			if len(selected) == 0 || !isSimilarMagnet(selected[0], best1080p) {
				selected = append(selected, best1080p)
				fmt.Printf("        ✅ 选择1080p版本: %s\n", best1080p.FileName)
			}
		}
	} else {
		// 只选择最佳的一个
		best := magnets[0]
		selected = append(selected, best)
		fmt.Printf("        ✅ 选择最佳%s字幕: %s (评分: %.1f)\n", 
			language, best.FileName, best.Score)
		
		// 检查是否有明显更大的文件值得同时下载
		for _, magnet := range magnets[1:] {
			if shouldSelectAdditional(best, magnet) {
				selected = append(selected, magnet)
				fmt.Printf("        ✅ 额外选择大文件: %s (评分: %.1f)\n", 
					magnet.FileName, magnet.Score)
				break // 最多选择2个
			}
		}
	}

	return selected
}

// groupByQuality 按质量分组
func groupByQuality(magnets []*ScoredMagnet) map[string][]*ScoredMagnet {
	groups := make(map[string][]*ScoredMagnet)
	
	for _, magnet := range magnets {
		quality := magnet.Quality
		if quality == "" || quality == "unknown" {
			quality = "standard"
		}
		groups[quality] = append(groups[quality], magnet)
	}
	
	return groups
}

// hasQuality 检查是否有指定质量
func hasQuality(groups map[string][]*ScoredMagnet, quality string) bool {
	_, exists := groups[quality]
	return exists
}

// getBestFromQuality 从指定质量组中获取最佳的
func getBestFromQuality(groups map[string][]*ScoredMagnet, quality string) *ScoredMagnet {
	magnets, exists := groups[quality]
	if !exists || len(magnets) == 0 {
		return nil
	}
	
	// 返回评分最高的
	best := magnets[0]
	for _, magnet := range magnets[1:] {
		if magnet.Score > best.Score {
			best = magnet
		}
	}
	
	return best
}

// shouldSelectAdditional 判断是否应该选择额外的磁力链接
func shouldSelectAdditional(best, candidate *ScoredMagnet) bool {
	// 如果文件大小差异超过50%，且候选文件更大，则选择
	if candidate.FileSize > best.FileSize {
		sizeDiff := float64(candidate.FileSize-best.FileSize) / float64(best.FileSize)
		if sizeDiff > 0.5 { // 大50%以上
			return true
		}
	}
	
	// 如果质量明显更高
	if isHigherQuality(candidate.Quality, best.Quality) {
		return true
	}
	
	return false
}

// isHigherQuality 判断质量是否更高
func isHigherQuality(quality1, quality2 string) bool {
	qualityRank := map[string]int{
		"4K":      4,
		"1080p":   3,
		"720p":    2,
		"480p":    1,
		"standard": 0,
		"unknown":  0,
	}
	
	rank1, exists1 := qualityRank[quality1]
	rank2, exists2 := qualityRank[quality2]
	
	if !exists1 || !exists2 {
		return false
	}
	
	return rank1 > rank2
}

// printSubtitleAnalysis 打印字幕分析结果
func printSubtitleAnalysis(magnets []*ScoredMagnet) {
	if len(magnets) == 0 {
		return
	}
	
	fmt.Printf("        📋 字幕版本详细分析:\n")
	for i, magnet := range magnets {
		sizeGB := float64(magnet.FileSize) / (1024 * 1024 * 1024)
		fmt.Printf("        %d. %s\n", i+1, magnet.FileName)
		fmt.Printf("           📊 评分: %.1f | 大小: %.2fGB | 质量: %s | 语言: %s\n",
			magnet.Score, sizeGB, magnet.Quality, magnet.SubtitleLanguage)
	}
}