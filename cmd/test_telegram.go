package main

import (
	"fmt"
	"log"
	"strings"
	"time"

	"magnet-downloader/internal/config"
	"magnet-downloader/internal/service"
	"magnet-downloader/pkg/telegram"
)

func main() {
	fmt.Println("🧪 测试Telegram通知功能")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("❌ 加载配置失败: %v", err)
	}

	// 创建Telegram服务
	telegramService := service.NewTelegramService(&cfg.Telegram)

	if !telegramService.IsEnabled() {
		fmt.Println("❌ Telegram服务未启用，请检查配置")
		return
	}

	fmt.Println("✅ Telegram服务已启用")

	// 测试采集完成通知
	fmt.Println("📤 发送采集完成通知...")
	scrapingStats := &telegram.ScrapingStats{
		TotalPages:       5,
		TotalMovies:      50,
		ProcessedMovies:  48,
		SuccessMovies:    45,
		FailedMovies:     3,
		NewMovies:        30,
		UpdatedMovies:    15,
		DownloadsCreated: 40,
		ImagesDownloaded: 90,
		StartTime:        time.Now().Add(-2 * time.Hour),
		EndTime:          time.Now(),
		Duration:         2 * time.Hour,
		SuccessRate:      93.75,
		ErrorCount:       3,
	}

	err = telegramService.SendScrapingCompleted(scrapingStats)
	if err != nil {
		fmt.Printf("❌ 发送采集完成通知失败: %v\n", err)
	} else {
		fmt.Println("✅ 采集完成通知发送成功")
	}

	// 等待一下
	time.Sleep(2 * time.Second)

	// 测试上传完成通知
	fmt.Println("📤 发送上传完成通知...")
	uploadStats := &telegram.UploadStats{
		TotalScans:        10,
		TotalUploads:      25,
		SuccessfulUploads: 23,
		FailedUploads:     2,
		TotalDataUploaded: 15 * 1024 * 1024 * 1024, // 15GB
		LastUploadTime:    time.Now(),
		UploadSuccessRate: 92.0,
		Duration:          45 * time.Minute,
	}

	err = telegramService.SendUploadCompleted(uploadStats)
	if err != nil {
		fmt.Printf("❌ 发送上传完成通知失败: %v\n", err)
	} else {
		fmt.Println("✅ 上传完成通知发送成功")
	}

	// 等待一下
	time.Sleep(2 * time.Second)

	// 测试上传失败通知
	fmt.Println("📤 发送上传失败通知...")
	err = telegramService.SendUploadFailed("test_video.mp4", "StreamTape", "网络连接超时")
	if err != nil {
		fmt.Printf("❌ 发送上传失败通知失败: %v\n", err)
	} else {
		fmt.Println("✅ 上传失败通知发送成功")
	}

	fmt.Println()
	fmt.Println("🎉 Telegram通知测试完成！")
}