#!/usr/bin/env python3
"""
VNC远程桌面管理工具
"""

import subprocess
import sys
import time
import os

def start_vnc():
    """启动VNC服务器"""
    print("🚀 启动VNC服务器...")
    
    try:
        # 停止可能存在的VNC会话
        subprocess.run(['vncserver', '-kill', ':1'], capture_output=True)
        time.sleep(2)
        
        # 启动VNC服务器
        result = subprocess.run([
            'vncserver', ':1', 
            '-geometry', '1920x1080',
            '-depth', '24'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ VNC服务器启动成功！")
            print("📱 连接信息:")
            print(f"   服务器地址: {get_server_ip()}:5901")
            print("   密码: javapi123")
            print("   分辨率: 1920x1080")
            print("\n💡 使用VNC客户端连接:")
            print("   - Windows: RealVNC Viewer, TightVNC")
            print("   - macOS: Screen Sharing, RealVNC Viewer")
            print("   - Linux: Re<PERSON><PERSON>, TigerVNC")
            return True
        else:
            print(f"❌ VNC启动失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 启动VNC时出错: {e}")
        return False

def stop_vnc():
    """停止VNC服务器"""
    print("🛑 停止VNC服务器...")
    
    try:
        result = subprocess.run(['vncserver', '-kill', ':1'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ VNC服务器已停止")
            return True
        else:
            print(f"❌ 停止VNC失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 停止VNC时出错: {e}")
        return False

def status_vnc():
    """查看VNC状态"""
    try:
        result = subprocess.run(['vncserver', '-list'], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("📊 VNC服务器状态:")
            print(result.stdout)
            
            # 检查端口
            port_result = subprocess.run(['netstat', '-tlnp'], capture_output=True, text=True)
            if ':5901' in port_result.stdout:
                print("✅ VNC服务器正在运行 (端口 5901)")
                print(f"🌐 连接地址: {get_server_ip()}:5901")
            else:
                print("❌ VNC服务器未运行")
        else:
            print("❌ 无法获取VNC状态")
            
    except Exception as e:
        print(f"❌ 检查VNC状态时出错: {e}")

def get_server_ip():
    """获取服务器IP地址"""
    try:
        # 尝试获取公网IP
        result = subprocess.run(['curl', '-s', 'ifconfig.me'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0 and result.stdout.strip():
            return result.stdout.strip()
    except:
        pass
    
    try:
        # 获取本地IP
        result = subprocess.run(['hostname', '-I'], capture_output=True, text=True)
        if result.returncode == 0:
            return result.stdout.strip().split()[0]
    except:
        pass
    
    return "YOUR_SERVER_IP"

def main():
    if len(sys.argv) < 2:
        print("VNC远程桌面管理工具")
        print("用法:")
        print("  python3 vnc_manager.py start   # 启动VNC服务器")
        print("  python3 vnc_manager.py stop    # 停止VNC服务器")
        print("  python3 vnc_manager.py status  # 查看VNC状态")
        print("  python3 vnc_manager.py restart # 重启VNC服务器")
        return
    
    command = sys.argv[1]
    
    if command == 'start':
        start_vnc()
    elif command == 'stop':
        stop_vnc()
    elif command == 'status':
        status_vnc()
    elif command == 'restart':
        print("🔄 重启VNC服务器...")
        stop_vnc()
        time.sleep(2)
        start_vnc()
    else:
        print(f"❌ 未知命令: {command}")

if __name__ == '__main__':
    main()
