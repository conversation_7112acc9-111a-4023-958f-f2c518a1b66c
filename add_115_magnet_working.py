#!/usr/bin/env python3
"""
115网盘磁力链接添加脚本 - 工作版
"""

import time
import sys
import subprocess
import os

def add_magnet_to_115_working(magnet_url):
    """通过115客户端界面添加磁力链接"""
    
    print(f"准备添加磁力链接: {magnet_url}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['DISPLAY'] = ':1'
    
    try:
        # 查找115窗口
        result = subprocess.run(['wmctrl', '-l'], capture_output=True, text=True, env=env)
        
        if not result.stdout:
            print("无法获取窗口列表")
            return False
        
        window_id = None
        for line in result.stdout.split('\n'):
            if '115' in line and line.strip():
                window_id = line.split()[0]
                print(f"找到115窗口: {window_id}")
                print(f"窗口标题: {line.strip()}")
                break
        
        if not window_id:
            print("未找到115客户端窗口")
            print("可用窗口列表:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")
            return False
        
        # 激活115窗口
        print("激活115窗口...")
        subprocess.run(['wmctrl', '-i', '-a', window_id], env=env)
        time.sleep(2)
        
        # 确保窗口获得焦点
        subprocess.run(['xdotool', 'windowfocus', window_id], env=env)
        time.sleep(1)
        
        print("开始自动操作...")
        
        # 尝试多种方式打开添加对话框
        # 方法1: 快捷键
        print("尝试快捷键...")
        subprocess.run(['xdotool', 'key', 'ctrl+shift+n'], env=env)
        time.sleep(2)
        subprocess.run(['xdotool', 'key', 'ctrl+n'], env=env)
        time.sleep(2)
        subprocess.run(['xdotool', 'key', 'ctrl+a'], env=env)
        time.sleep(2)
        
        # 方法2: 点击可能的按钮位置
        print("尝试点击界面...")
        # 点击页面中央偏上的位置
        subprocess.run(['xdotool', 'mousemove', '500', '200'], env=env)
        subprocess.run(['xdotool', 'click', '1'], env=env)
        time.sleep(1)
        
        # 点击左上角可能的添加按钮
        subprocess.run(['xdotool', 'mousemove', '150', '150'], env=env)
        subprocess.run(['xdotool', 'click', '1'], env=env)
        time.sleep(1)
        
        # 输入磁力链接
        print("输入磁力链接...")
        subprocess.run(['xdotool', 'type', '--delay', '100', magnet_url], env=env)
        time.sleep(2)
        
        # 确认操作
        print("确认操作...")
        subprocess.run(['xdotool', 'key', 'Return'], env=env)
        time.sleep(1)
        subprocess.run(['xdotool', 'key', 'Return'], env=env)
        time.sleep(1)
        
        # 尝试Tab+Enter组合
        subprocess.run(['xdotool', 'key', 'Tab', 'Return'], env=env)
        time.sleep(1)
        
        print("操作完成")
        return True
        
    except Exception as e:
        print(f"操作失败: {e}")
        return False

def main():
    if len(sys.argv) != 2:
        print("用法: python3 add_115_magnet_working.py <磁力链接>")
        sys.exit(1)
    
    magnet_url = sys.argv[1]
    
    if not magnet_url.startswith("magnet:"):
        print("错误: 请提供有效的磁力链接")
        sys.exit(1)
    
    print("=" * 60)
    print("115网盘磁力链接自动添加工具")
    print("=" * 60)
    
    success = add_magnet_to_115_working(magnet_url)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ 自动操作已完成！")
        print("📋 磁力链接已发送到115客户端")
        print("🔍 请检查115客户端的云下载任务列表")
    else:
        print("❌ 自动操作失败！")
    
    print("\n🔧 手动操作步骤:")
    print("1. 打开115客户端")
    print("2. 点击 '云下载' 标签")
    print("3. 点击 '添加任务' 按钮")
    print("4. 粘贴磁力链接:")
    print(f"   {magnet_url}")
    print("5. 点击 '确定' 开始下载")
    print("=" * 60)

if __name__ == "__main__":
    main()